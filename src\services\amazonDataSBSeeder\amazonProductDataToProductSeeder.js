const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
const {
    targetDbConfig,
    migrationConfig,
    fieldMapping,
    validationRules,
    getNestedValue,
    validateProductData,
    sanitizeProductData,
    testConnection,
    checkTargetTable,
} = require("./amazonProductDataToProductConfig");

// Utility function to extract seller information from storefront URL
function extractSellerInfoFromStorefrontUrl(storefrontUrl) {
    if (!storefrontUrl || typeof storefrontUrl !== 'string') {
        return { sellerId: null, marketplace: null, sellerUrl: null };
    }

    try {
        // Parse the URL to get domain and query parameters
        const url = new URL(storefrontUrl.startsWith('http') ? storefrontUrl : `https://${storefrontUrl}`);

        // Extract marketplace from domain
        let marketplace = null;
        if (url.hostname.includes('amazon.com')) {
            marketplace = 'US';
        } else if (url.hostname.includes('amazon.co.uk')) {
            marketplace = 'UK';
        } else if (url.hostname.includes('amazon.ca')) {
            marketplace = 'CA';
        } else if (url.hostname.includes('amazon.de')) {
            marketplace = 'DE';
        } else if (url.hostname.includes('amazon.fr')) {
            marketplace = 'FR';
        } else if (url.hostname.includes('amazon.it')) {
            marketplace = 'IT';
        } else if (url.hostname.includes('amazon.es')) {
            marketplace = 'ES';
        } else if (url.hostname.includes('amazon.nl')) {
            marketplace = 'NL';
        } else if (url.hostname.includes('amazon.se')) {
            marketplace = 'SE';
        } else if (url.hostname.includes('amazon.pl')) {
            marketplace = 'PL';
        } else if (url.hostname.includes('amazon.com.au')) {
            marketplace = 'AU';
        } else if (url.hostname.includes('amazon.com.br')) {
            marketplace = 'BR';
        } else if (url.hostname.includes('amazon.com.mx')) {
            marketplace = 'MX';
        } else if (url.hostname.includes('amazon.in')) {
            marketplace = 'IN';
        } else if (url.hostname.includes('amazon.co.jp')) {
            marketplace = 'JP';
        } else if (url.hostname.includes('amazon.sg')) {
            marketplace = 'SG';
        } else if (url.hostname.includes('amazon.ae')) {
            marketplace = 'AE';
        } else if (url.hostname.includes('amazon.sa')) {
            marketplace = 'SA';
        } else if (url.hostname.includes('amazon.eg')) {
            marketplace = 'EG';
        } else if (url.hostname.includes('amazon.ng')) {
            marketplace = 'NG';
        } else if (url.hostname.includes('amazon.ke')) {
            marketplace = 'KE';
        } else if (url.hostname.includes('amazon.za')) {
            marketplace = 'ZA';
        } else if (url.hostname.includes('amazon.tr')) {
            marketplace = 'TR';
        } else if (url.hostname.includes('amazon.sa')) {
            marketplace = 'SA';
        } else if (url.hostname.includes('amazon.ae')) {
            marketplace = 'AE';
        } else if (url.hostname.includes('amazon.eg')) {
            marketplace = 'EG';
        } else if (url.hostname.includes('amazon.ng')) {
            marketplace = 'NG';
        } else if (url.hostname.includes('amazon.ke')) {
            marketplace = 'KE';
        } else if (url.hostname.includes('amazon.za')) {
            marketplace = 'ZA';
        } else if (url.hostname.includes('amazon.tr')) {
            marketplace = 'TR';
        } else {
            // Extract country code from domain
            const domainMatch = url.hostname.match(/amazon\.([a-z]{2,3})/);
            if (domainMatch) {
                marketplace = domainMatch[1].toUpperCase();
            }
        }

        // Extract seller ID from query parameters
        let sellerId = null;
        const sellerParam = url.searchParams.get('seller');
        if (sellerParam) {
            sellerId = sellerParam;
        }

        return {
            sellerId,
            marketplace,
            sellerUrl: storefrontUrl
        };
    } catch (error) {
        console.warn(`Failed to parse storefront URL: ${storefrontUrl}`, error.message);
        return { sellerId: null, marketplace: null, sellerUrl: null };
    }
}

// Helper function to create target database connection
// You can use either Prisma client or direct database connection
async function getTargetDbConnection() {
    // Option 1: Using Prisma client for target database
    // const { PrismaClient: TargetPrismaClient } = require("@prisma/client");
    // const targetPrisma = new TargetPrismaClient({
    //   datasources: {
    //     db: {
    //       url: `postgresql://${targetDbConfig.username}:${targetDbConfig.password}@${targetDbConfig.host}:${targetDbConfig.port}/${targetDbConfig.database}`
    //     }
    //   }
    // });
    // return targetPrisma;

    // Option 2: Using direct database connection (example with pg)
    const { Client } = require('pg');

    console.log(`🔌 Creating connection to: ${targetDbConfig.host}:${targetDbConfig.port}/${targetDbConfig.database}`);

    const client = new Client({
        host: targetDbConfig.host,
        port: targetDbConfig.port,
        database: targetDbConfig.database,
        user: targetDbConfig.username,
        password: targetDbConfig.password,
        ssl: targetDbConfig.ssl,
        connectionTimeoutMillis: targetDbConfig.connectionTimeoutMillis,
        idleTimeoutMillis: targetDbConfig.idleTimeoutMillis,
    });

    try {
        await client.connect();
        console.log('✅ Successfully connected to target database');
        return client;
    } catch (error) {
        console.error('❌ Failed to connect to target database:', error.message);
        throw error;
    }
}



// Helper function to extract and transform data from AmazonProductData
async function transformAmazonDataToProduct(amazonProductData) {
    const data = amazonProductData.data;

    // Extract seller information from company data
    const company = amazonProductData.company;
    const sellerUrl = company?.storeFrontURL || '';

    // Parse seller information from URL
    let sellerId = null;
    let marketplace = null;

    if (sellerUrl) {
        try {
            const url = new URL(sellerUrl);
            // Extract marketplace from domain (e.g., amazon.com -> US, amazon.de -> DE, amazon.co.uk -> UK)
            const hostname = url.hostname.replace('www.', '');
            if (hostname.includes('amazon.de')) {
                marketplace = 'DE';
            } else if (hostname.includes('amazon.co.uk')) {
                marketplace = 'UK';
            } else if (hostname.includes('amazon.com')) {
                marketplace = 'US';
            } else if (hostname.includes('amazon.ca')) {
                marketplace = 'CA';
            } else if (hostname.includes('amazon.fr')) {
                marketplace = 'FR';
            } else if (hostname.includes('amazon.it')) {
                marketplace = 'IT';
            } else if (hostname.includes('amazon.es')) {
                marketplace = 'ES';
            } else if (hostname.includes('amazon.nl')) {
                marketplace = 'NL';
            } else if (hostname.includes('amazon.se')) {
                marketplace = 'SE';
            } else if (hostname.includes('amazon.pl')) {
                marketplace = 'PL';
            } else if (hostname.includes('amazon.com.au')) {
                marketplace = 'AU';
            } else if (hostname.includes('amazon.com.br')) {
                marketplace = 'BR';
            } else if (hostname.includes('amazon.com.mx')) {
                marketplace = 'MX';
            } else if (hostname.includes('amazon.in')) {
                marketplace = 'IN';
            } else if (hostname.includes('amazon.co.jp')) {
                marketplace = 'JP';
            } else {
                // Fallback: extract country code from domain
                const domainParts = hostname.split('.');
                if (domainParts.length >= 2) {
                    const lastPart = domainParts[domainParts.length - 1];
                    if (lastPart.length === 2) {
                        marketplace = lastPart.toUpperCase();
                    } else {
                        marketplace = hostname;
                    }
                } else {
                    marketplace = hostname;
                }
            }

            // Extract seller ID from URL parameters
            // Try different parameter names that Amazon uses
            const meParam = url.searchParams.get('me');
            const sellerParam = url.searchParams.get('seller');

            if (sellerParam) {
                sellerId = sellerParam;
            } else if (meParam) {
                sellerId = meParam;
            }
        } catch (error) {
            console.warn(`Failed to parse seller URL: ${sellerUrl}`, error.message);
        }
    }

    // Use the field mapping configuration to extract data
    const transformedProduct = {
        // Add seller information
        sellerId: sellerId,
        marketplace: marketplace,
        sellerUrl: sellerUrl
    };

    // Map each field using the configuration
    Object.entries(fieldMapping).forEach(([targetField, sourcePath]) => {
        if (sourcePath === null) {
            // Handle fields that need special processing
            switch (targetField) {
                case 'url':
                    // Extract URL and use "N/A" if null/undefined
                    const productUrl = data.productData?.[0]?.url;
                    transformedProduct[targetField] = productUrl || "N/A";
                    break;
                case 'star_5_count':
                case 'star_4_count':
                case 'star_3_count':
                case 'star_2_count':
                case 'star_1_count':
                    // Extract from review data if available
                    const reviewData = data.productData?.[0]?.review || {};

                    // The structure is: reviewPerStar[{"5starReview": 3}, {"4starReview": 1}, {"3starReview": 0}, {"2starReview": 0}, {"1starReview": 0}]
                    // Array is ordered from 5-star to 1-star
                    let starCount = null;
                    const starNumber = targetField.split('_')[1];

                    if (reviewData.reviewPerStar && Array.isArray(reviewData.reviewPerStar)) {
                        // Find the object that contains the specific star review
                        const starObject = reviewData.reviewPerStar.find(obj => obj[`${starNumber}starReview`] !== undefined);
                        if (starObject) {
                            starCount = starObject[`${starNumber}starReview`];
                        }
                    }

                    transformedProduct[targetField] = starCount;
                    break;
                case 'main_image_url':
                    // Extract main image URL if available
                    const productData = data.productData?.[0] || {};
                    transformedProduct[targetField] = productData.productMainImage || null;
                    break;
                case 'secondary_images':
                    // Extract secondary images if available
                    const productDataForImages = data.productData?.[0] || {};
                    transformedProduct[targetField] = productDataForImages.secondaryImages && Array.isArray(productDataForImages.secondaryImages) ? JSON.stringify(productDataForImages.secondaryImages) : null;
                    break;
                case 'brand_story_images':
                    // Extract brand story images if available
                    const brandStoryData = data.productData?.[0]?.brandStory || {};
                    transformedProduct[targetField] = brandStoryData.images ? JSON.stringify(brandStoryData.images) : null;
                    break;
                case 'premium_aplus_present':
                    // Check for premium A+ content
                    const aplusData = data.productData?.[0]?.AplusContent || {};
                    transformedProduct[targetField] = aplusData.premiumAplusPresent || false;
                    break;
                case 'brand_story_present':
                    // Check for brand story
                    const brandStory = data.productData?.[0]?.brandStory || {};
                    transformedProduct[targetField] = brandStory.brandStoryPresent || false;
                    break;
                case 'out_of_stock':
                    // Check for out of stock status
                    const stockData = data.productData?.[0]?.stock || {};
                    transformedProduct[targetField] = Boolean(stockData.outOfStock || false);
                    break;
                default:
                    transformedProduct[targetField] = null;
            }
        } else {
            // Extract value using the source path
            let value = getNestedValue(amazonProductData, sourcePath);

            // Handle special cases
            if (targetField === 'bullet_points' && value) {
                value = JSON.stringify(value);
            } else if (targetField === 'categories_and_ranks' && value) {
                value = JSON.stringify(value);
            } else if (targetField === 'full_json_data') {
                value = JSON.stringify(value);
            } else if (targetField === 'price' && value) {
                value = parseFloat(value);
            } else if (targetField === 'rating' && value) {
                value = parseFloat(value);
            }

            transformedProduct[targetField] = value;
        }
    });

    // Extract seller information from Company's storefront URL
    let sellerInfo = { sellerId: null, marketplace: null, sellerUrl: null };

    try {
        // Get the company data to access storefront URL
        const company = await prisma.company.findUnique({
            where: { id: amazonProductData.companyId },
            select: { storeFrontURL: true }
        });

        if (company && company.storeFrontURL) {
            sellerInfo = extractSellerInfoFromStorefrontUrl(company.storeFrontURL);
        }
    } catch (error) {
        console.warn(`Failed to get company data for AmazonProductData ID ${amazonProductData.id}:`, error.message);
    }

    // Add seller information to the transformed product
    transformedProduct.sellerId = sellerInfo.sellerId;
    transformedProduct.marketplace = sellerInfo.marketplace;
    transformedProduct.sellerUrl = sellerInfo.sellerUrl;

    // Set createdAt and updatedAt properly
    transformedProduct.createdAt = amazonProductData.updatedAt || new Date();
    transformedProduct.updatedAt = new Date(); // Always set to current timestamp

    // Ensure full_json_data is never null
    if (!transformedProduct.full_json_data) {
        transformedProduct.full_json_data = {};
    }

    // Validate and sanitize the data
    const validationErrors = validateProductData(transformedProduct);
    if (validationErrors.length > 0) {
        console.warn(`Validation warnings for AmazonProductData ID ${amazonProductData.id}:`, validationErrors);
    }

    const sanitizedProduct = sanitizeProductData(transformedProduct);

    return sanitizedProduct;
}

// Main seeder function
async function seedAmazonProductDataToProduct() {
    let targetDb = null;

    try {
        console.log('Starting AmazonProductData to Product migration...');

        // Test connection and table existence
        console.log('Testing database connection...');
        const connectionOk = await testConnection();
        if (!connectionOk) {
            throw new Error('Failed to connect to target database');
        }

        console.log('Checking target table...');
        const tableExists = await checkTargetTable();
        if (!tableExists) {
            throw new Error('Target table "products" does not exist');
        }

        // Get target database connection
        targetDb = await getTargetDbConnection();

        // Fetch AmazonProductData records that haven't been pushed to SB yet
        const amazonProductDataList = await prisma.amazonProductData.findMany({
            where: {
                pushedToSB: false
            },
        });

        console.log(`Found ${amazonProductDataList.length} AmazonProductData records to migrate`);

        let successCount = 0;
        let errorCount = 0;
        let validationErrorCount = 0;

        // Process records in batches to avoid memory issues
        const batchSize = migrationConfig.batchSize;
        for (let i = 0; i < amazonProductDataList.length; i += batchSize) {
            const batch = amazonProductDataList.slice(i, i + batchSize);

            console.log(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(amazonProductDataList.length / batchSize)}`);

            for (const amazonProductData of batch) {
                try {
                    // Transform the data
                    const transformedProduct = transformAmazonDataToProduct(amazonProductData);

                    // Validate the transformed data
                    const validationErrors = validateProductData(transformedProduct);
                    if (validationErrors.length > 0) {
                        console.warn(`Validation errors for AmazonProductData ID ${amazonProductData.id}:`, validationErrors);
                        validationErrorCount++;

                        // Skip records with critical validation errors
                        const criticalErrors = validationErrors.filter(error =>
                            error.includes('Missing required field') || error.includes('Invalid URL format')
                        );
                        if (criticalErrors.length > 0) {
                            console.log(`Skipping AmazonProductData ID ${amazonProductData.id} due to critical validation errors`);
                            continue;
                        }
                    }

                    // Always insert new row - track history of all product data changes
                    console.log(`📝 Inserting new row for Product with URL ${transformedProduct.url}`);

                    // Insert into target database
                    const insertQuery = `
            INSERT INTO products (
              "sellerId", marketplace, "sellerUrl", url, brand_name, product_title, description, price,
              rating, total_reviews, review_category, star_5_count, star_4_count,
              star_3_count, star_2_count, star_1_count, sales_count,
              main_image_url, image_count, video_count,
              title_char_count, title_under_150_chars, out_of_stock,
              aplus_content_present, premium_aplus_present, brand_story_present,
              storefront_present, storefront_url,
              bullet_points, categories_and_ranks, secondary_images, brand_story_images,
              full_json_data, "createdAt", "updatedAt"
            ) VALUES (
              $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35
            ) RETURNING id
          `;

                    const values = [
                        transformedProduct.sellerId,
                        transformedProduct.marketplace,
                        transformedProduct.sellerUrl,
                        transformedProduct.url,
                        transformedProduct.brand_name,
                        transformedProduct.product_title,
                        transformedProduct.description,
                        transformedProduct.price,
                        transformedProduct.rating,
                        transformedProduct.total_reviews,
                        transformedProduct.review_category,
                        transformedProduct.star_5_count,
                        transformedProduct.star_4_count,
                        transformedProduct.star_3_count,
                        transformedProduct.star_2_count,
                        transformedProduct.star_1_count,
                        transformedProduct.sales_count,
                        transformedProduct.main_image_url,
                        transformedProduct.image_count,
                        transformedProduct.video_count,
                        transformedProduct.title_char_count,
                        transformedProduct.title_under_150_chars,
                        transformedProduct.out_of_stock,
                        transformedProduct.aplus_content_present,
                        transformedProduct.premium_aplus_present,
                        transformedProduct.brand_story_present,
                        transformedProduct.storefront_present,
                        transformedProduct.storefront_url,
                        transformedProduct.bullet_points,
                        transformedProduct.categories_and_ranks,
                        transformedProduct.secondary_images,
                        transformedProduct.brand_story_images,
                        transformedProduct.full_json_data,
                        transformedProduct.createdAt,
                        transformedProduct.updatedAt
                    ];

                    const result = await targetDb.query(insertQuery, values);

                    // Mark as pushed to SB
                    await prisma.amazonProductData.update({
                        where: { id: amazonProductData.id },
                        data: { pushedToSB: true }
                    });

                    console.log(`Successfully migrated AmazonProductData ID ${amazonProductData.id} to Product ID ${result.rows[0].id}`);
                    successCount++;

                } catch (error) {
                    console.error(`Error migrating AmazonProductData ID ${amazonProductData.id}:`, error.message);
                    errorCount++;
                }
            }
        }

        console.log('\n=== Migration Summary ===');
        console.log(`Total records processed: ${amazonProductDataList.length}`);
        console.log(`Successfully migrated: ${successCount}`);
        console.log(`Validation errors: ${validationErrorCount}`);
        console.log(`Database errors: ${errorCount}`);
        console.log('========================\n');

    } catch (error) {
        console.error('Migration failed:', error);
        throw error;
    } finally {
        // Clean up connections
        if (targetDb) {
            await targetDb.end();
        }
        await prisma.$disconnect();
    }
}

// Function to migrate specific records by ID
async function migrateSpecificRecords(recordIds) {
    let targetDb = null;

    try {
        console.log(`Starting migration for specific records: ${recordIds.join(', ')}`);

        targetDb = await getTargetDbConnection();

        const amazonProductDataList = await prisma.amazonProductData.findMany({
            where: {
                id: {
                    in: recordIds
                }
            },
            include: {
                company: true
            }
        });

        console.log(`Found ${amazonProductDataList.length} records to migrate`);

        for (const amazonProductData of amazonProductDataList) {
            try {
                const transformedProduct = await transformAmazonDataToProduct(amazonProductData);

                // Always insert new row - track history of all product data changes
                console.log(`📝 Inserting new row for Product with URL ${transformedProduct.url}`);

                // Insert new product
                const insertQuery = `
            INSERT INTO products (
              "sellerId", marketplace, "sellerUrl", url, brand_name, product_title, description, price,
              rating, total_reviews, review_category, star_5_count, star_4_count,
              star_3_count, star_2_count, star_1_count, sales_count,
              main_image_url, image_count, video_count,
              title_char_count, title_under_150_chars, out_of_stock,
              aplus_content_present, premium_aplus_present, brand_story_present,
              storefront_present, storefront_url,
              bullet_points, categories_and_ranks, secondary_images, brand_story_images,
              full_json_data, "createdAt", "updatedAt", marketplace, "sellerId", "sellerUrl"
            ) VALUES (
              $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35
            ) RETURNING id
          `;

                const values = [
                    transformedProduct.sellerId,
                    transformedProduct.marketplace,
                    transformedProduct.sellerUrl,
                    transformedProduct.url,
                    transformedProduct.brand_name,
                    transformedProduct.product_title,
                    transformedProduct.description,
                    transformedProduct.price,
                    transformedProduct.rating,
                    transformedProduct.total_reviews,
                    transformedProduct.review_category,
                    transformedProduct.star_5_count,
                    transformedProduct.star_4_count,
                    transformedProduct.star_3_count,
                    transformedProduct.star_2_count,
                    transformedProduct.star_1_count,
                    transformedProduct.sales_count,
                    transformedProduct.main_image_url,
                    transformedProduct.image_count,
                    transformedProduct.video_count,
                    transformedProduct.title_char_count,
                    transformedProduct.title_under_150_chars,
                    transformedProduct.out_of_stock,
                    transformedProduct.aplus_content_present,
                    transformedProduct.premium_aplus_present,
                    transformedProduct.brand_story_present,
                    transformedProduct.storefront_present,
                    transformedProduct.storefront_url,
                    transformedProduct.bullet_points,
                    transformedProduct.categories_and_ranks,
                    transformedProduct.secondary_images,
                    transformedProduct.brand_story_images,
                    transformedProduct.full_json_data,
                    transformedProduct.createdAt,
                    transformedProduct.updatedAt,
                    transformedProduct.marketplace,
                    transformedProduct.sellerId,
                    transformedProduct.sellerUrl
                ];

                const result = await targetDb.query(insertQuery, values);

                // Mark as pushed to SB
                await prisma.amazonProductData.update({
                    where: { id: amazonProductData.id },
                    data: { pushedToSB: true }
                });

                console.log(`Successfully migrated AmazonProductData ID ${amazonProductData.id} to Product ID ${result.rows[0].id}`);

            } catch (error) {
                console.error(`Error migrating AmazonProductData ID ${amazonProductData.id}:`, error.message);
            }
        }

    } catch (error) {
        console.error('Migration failed:', error);
        throw error;
    } finally {
        if (targetDb) {
            await targetDb.end();
        }
        await prisma.$disconnect();
    }
}

async function migrateFilteredRecords(amazonProductDataList) {
    let targetDb = null;

    try {
        console.log(`Starting migration for ${amazonProductDataList.length} filtered records`);

        // Check target table
        console.log('Checking target table...');
        const tableExists = await checkTargetTable();
        if (!tableExists) {
            throw new Error('Target table "products" does not exist');
        }

        // Get target database connection
        targetDb = await getTargetDbConnection();

        let successCount = 0;
        let errorCount = 0;
        let skippedCount = 0;
        let validationErrorCount = 0;

        // Process records in batches to avoid memory issues
        const batchSize = migrationConfig.batchSize;
        for (let i = 0; i < amazonProductDataList.length; i += batchSize) {
            const batch = amazonProductDataList.slice(i, i + batchSize);

            console.log(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(amazonProductDataList.length / batchSize)}`);

            for (const amazonProductData of batch) {
                try {
                    // Transform the data
                    const transformedProduct = await transformAmazonDataToProduct(amazonProductData);

                    // Validate the transformed data
                    const validationErrors = validateProductData(transformedProduct);
                    if (validationErrors.length > 0) {
                        console.warn(`Validation errors for AmazonProductData ID ${amazonProductData.id}:`, validationErrors);
                        validationErrorCount++;

                        // Skip records with critical validation errors
                        const criticalErrors = validationErrors.filter(error =>
                            error.includes('Missing required field') || error.includes('Invalid URL format')
                        );
                        if (criticalErrors.length > 0) {
                            console.log(`Skipping AmazonProductData ID ${amazonProductData.id} due to critical validation errors`);
                            continue;
                        }
                    }

                    // Always insert new row - track history of all product data changes
                    console.log(`📝 Inserting new row for Product with URL ${transformedProduct.url}`);

                    // Insert into target database
                    const insertQuery = `
                        INSERT INTO products (
                          "sellerId", marketplace, "sellerUrl", url, brand_name, product_title, description, price,
                          rating, total_reviews, review_category, star_5_count, star_4_count,
                          star_3_count, star_2_count, star_1_count, sales_count,
                          main_image_url, image_count, video_count,
                          title_char_count, title_under_150_chars, out_of_stock,
                          aplus_content_present, premium_aplus_present, brand_story_present,
                          storefront_present, storefront_url,
                          bullet_points, categories_and_ranks, secondary_images, brand_story_images,
                          full_json_data, "createdAt", "updatedAt"
                        ) VALUES (
                          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35
                        ) RETURNING id
                    `;

                    const values = [
                        transformedProduct.sellerId,
                        transformedProduct.marketplace,
                        transformedProduct.sellerUrl,
                        transformedProduct.url,
                        transformedProduct.brand_name,
                        transformedProduct.product_title,
                        transformedProduct.description,
                        transformedProduct.price,
                        transformedProduct.rating,
                        transformedProduct.total_reviews,
                        transformedProduct.review_category,
                        transformedProduct.star_5_count,
                        transformedProduct.star_4_count,
                        transformedProduct.star_3_count,
                        transformedProduct.star_2_count,
                        transformedProduct.star_1_count,
                        transformedProduct.sales_count,
                        transformedProduct.main_image_url,
                        transformedProduct.image_count,
                        transformedProduct.video_count,
                        transformedProduct.title_char_count,
                        transformedProduct.title_under_150_chars,
                        transformedProduct.out_of_stock,
                        transformedProduct.aplus_content_present,
                        transformedProduct.premium_aplus_present,
                        transformedProduct.brand_story_present,
                        transformedProduct.storefront_present,
                        transformedProduct.storefront_url,
                        transformedProduct.bullet_points,
                        transformedProduct.categories_and_ranks,
                        transformedProduct.secondary_images,
                        transformedProduct.brand_story_images,
                        transformedProduct.full_json_data,
                        transformedProduct.createdAt,
                        transformedProduct.updatedAt
                    ];

                    const result = await targetDb.query(insertQuery, values);

                    // Mark as pushed to SB
                    await prisma.amazonProductData.update({
                        where: { id: amazonProductData.id },
                        data: { pushedToSB: true }
                    });

                    console.log(`Successfully migrated AmazonProductData ID ${amazonProductData.id} to Product ID ${result.rows[0].id}`);
                    successCount++;

                } catch (error) {
                    console.error(`Error migrating AmazonProductData ID ${amazonProductData.id}:`, error.message);
                    errorCount++;
                }
            }
        }

        console.log('\n=== Migration Summary ===');
        console.log(`Total records processed: ${amazonProductDataList.length}`);
        console.log(`Successfully migrated: ${successCount}`);
        console.log(`Validation errors: ${validationErrorCount}`);
        console.log(`Database errors: ${errorCount}`);
        console.log('========================\n');

    } catch (error) {
        console.error('Migration failed:', error);
        throw error;
    } finally {
        // Clean up connections
        if (targetDb) {
            await targetDb.end();
        }
    }
}

async function getAmazonProductDataRecordToMigrate() {
    console.log("Getting AmazonProductData records to migrate...");

    // Calculate date 4 weeks ago
    const fourWeeksAgo = new Date();
    fourWeeksAgo.setDate(fourWeeksAgo.getDate() - 28); // 4 weeks = 28 days

    const amazonProductDataList = await prisma.amazonProductData.findMany({
        where: {
            status: "completed",
            pushedToSB: false,
            company: {
                storeFrontURL: {
                    not: ""
                }
            }
        },
        include: {
            company: true
        },
        orderBy: {
            updatedAt: 'desc'
        }
    });

    console.log("Total records to migrate (updated within 4 weeks):", amazonProductDataList.length);
    // console.log("Date threshold:", fourWeeksAgo.toISOString());

    if (amazonProductDataList.length > 0) {
        console.log("Starting migration of filtered records...");
        await migrateFilteredRecords(amazonProductDataList);
    } else {
        console.log("No records to migrate.");
    }
}


// Export functions
module.exports = {
    migrateSpecificRecords,
    transformAmazonDataToProduct,
};


async function main() {
    try {
        console.log("Starting migration...");
        await getAmazonProductDataRecordToMigrate();
    } catch (error) {
        console.error('Error:', error);
    } finally {
        await prisma.$disconnect();
    }
}

main();