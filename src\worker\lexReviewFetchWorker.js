const cron = require("node-cron");
const { PrismaClient } = require("@prisma/client");
const axios = require("axios");
const {
  restartServerSafely,
  getRestartStatus,
} = require("../utils/azureUtils/serverActions");
const { sendMessageToSlack } = require("../utils/slack");
const { LEX_TAGGED_USERS } = require("../services/scrapeAmazon/constant");
const prisma = new PrismaClient();

// Tagged users for Slack notifications

class LexReviewFetchWorker {
  constructor(workerManager = null) {
    this.BASE_URL = process.env.LEX_SCRAPER_BASE_URL || "http://localhost:3001";
    this.isRunning = false;
    this.pollInterval = 5000; // 30 seconds between polls
    this.workerManager = workerManager; // Reference to the worker manager for global state
  }

  async markCookiesAsExpired(expiredOnes) {
    const expiredOnesList = Array.from(expiredOnes);
    await prisma.lexReviewScraperCookies.updateMany({
      where: { id: { in: expiredOnesList } },
      data: {
        cookieStatus: "EXPIRE",
        active: false,
        updatedAt: new Date(),
      },
    });

    const expiredCookies = await prisma.lexReviewScraperCookies.findMany({
      where: { id: { in: expiredOnesList } },
      select: { id: true, emailId: true },
    });

    await sendMessageToSlack(
      process.env.LEX_NOTI_SLACK_WEBHOOK_URL,
      `${LEX_TAGGED_USERS.join(" ")} Lex Cookies expired for: ${expiredCookies
        .map(
          (cookie) =>
            `${cookie.emailId} ${cookie.countryCode || ""} (ID: ${cookie.id})`
        )
        .join(", ")}`
    );
  }

  async clearDatabase() {
    const response = await axios.post(`${this.BASE_URL}/api/clear_db`, {
      headers: {
        "Content-Type": "application/json",
      },
      timeout: 30000,
    });
    return;
  }

  async start() {
    console.log("🚀 Starting Lex Review Fetch Worker...");

    // Run every minute to check for completed jobs
    cron.schedule("*/0.5 * * * *", async () => {
      if (this.isRunning) {
        console.log("⏳ Review fetch worker already running, skipping...");
        return;
      }

      try {
        this.isRunning = true;
        await this.processReviews();
      } catch (error) {
        console.error("❌ Error in review fetch worker:", error);
      } finally {
        this.isRunning = false;
      }
    });

    console.log("✅ Lex Review Fetch Worker scheduled to run every minute");
  }

  async processReviews() {
    console.log("🔄 Processing review fetching...");

    try {
      // Call the get_reviews API directly
      const response = await axios.get(`${this.BASE_URL}/api/get_reviews`, {
        headers: {
          "Content-Type": "application/json",
        },
        timeout: 60000, // 60 second timeout for review fetching
      });

      if (response.data && Array.isArray(response.data)) {
        console.log(`📥 Received ${response.data.length} ASIN results`);
        await this.processDirectReviews(response.data);
      } else {
        console.log("📭 No review data found in response");
      }
    } catch (error) {
      console.error("❌ Error processing reviews:", error);
    }
  }

  async processDirectReviews(asinResults) {
    try {
      let totalProcessed = 0;
      let totalReviews = 0;
      await this.checkAndRestartServerIfNeeded(asinResults);

      for (const asinResult of asinResults) {
        const { asin, status, reviews, countryCode, cookieId } = asinResult;

        if (!asin) {
          console.warn(`⚠️ Missing ASIN in response item`);
          continue;
        }

        if (status === "COOKIE_EXPIRED") {
          this.markCookiesAsExpired([cookieId]);
        }

        console.log(
          `📊 Processing ASIN ${asin}: Status ${status}, ${
            reviews ? reviews.length : 0
          } reviews`
        );

        // Update ASIN status in database
        try {
          await this.updateAsinStatus(asin, status, reviews, countryCode);
        } catch (asinUpdateError) {
          console.error(
            `❌ Error updating ASIN ${asin} status:`,
            asinUpdateError.message
          );
        }

        // Process reviews if any exist
        if (Array.isArray(reviews) && reviews.length > 0) {
          await this.saveDirectReviews(reviews, asin, countryCode);
          totalReviews += reviews.length;
        }

        totalProcessed++;
      }

      console.log(
        `✅ Processed ${totalProcessed} ASINs with ${totalReviews} total reviews`
      );

      // Check if all ASINs are processed and restart server if needed
    } catch (error) {
      console.error("❌ Error processing direct reviews:", error);
      throw error;
    }
  }

  async updateAsinStatus(asin, status, reviewData = null, countryCode = "US") {
    try {
      const updateData = {
        status: status === "SUCCESS" ? "REVIEW_SCRAPED" : "REVIEW_PENDING",
        updatedAt: new Date(),
      };

      // Extract additional ASIN information from review data if available
      if (reviewData && reviewData.length > 0) {
        const firstReview = reviewData[0];
        let parsedData;

        if (typeof firstReview.data === "string") {
          parsedData = JSON.parse(firstReview.data);
        } else {
          parsedData = firstReview.data || firstReview;
        }

        // Update ASIN with product information from review data
        if (parsedData.productTitle) {
          updateData.title = parsedData.productTitle;
        }
        if (parsedData.productLink) {
          updateData.productLink = parsedData.productLink;
        }
        if (parsedData["Seller Name"]) {
          updateData.sellerName = parsedData["Seller Name"];
        }

        // Extract country code from Amazon domain in productLink
        if (parsedData.productLink) {
          const extractedCountryCode = this.extractCountryCodeFromUrl(
            parsedData.productLink
          );
          if (extractedCountryCode) {
            countryCode = extractedCountryCode;
          }
        }
      }

      // First, ensure the ASIN exists in the database using compound unique key
      await prisma.lexASIN.upsert({
        where: {
          asin_countryCode: {
            asin: asin,
            countryCode: countryCode,
          },
        },
        update: {
          ...updateData,
        },
        create: {
          asin: asin,
          ...updateData,
          createdAt: new Date(),
        },
      });
    } catch (error) {
      console.error(`❌ Error updating ASIN ${asin}:`, error);
      throw error;
    }
  }

  extractCountryCodeFromUrl(url) {
    try {
      if (!url) return null;

      // Extract country code from Amazon domain
      const domainMatch = url.match(/amazon\.([a-z\.]+)/);
      if (!domainMatch) return null;

      const domain = domainMatch[1];
      const countryMapping = {
        com: "US",
        "co.uk": "UK",
        de: "DE",
        ca: "CA",
        fr: "FR",
        in: "IN",
      };

      return countryMapping[domain] || "US";
    } catch (error) {
      console.error("Error extracting country code:", error);
      return null;
    }
  }

  async saveDirectReviews(reviews, asin, countryCode) {
    try {
      let savedCount = 0;
      let updatedCount = 0;

      for (const reviewItem of reviews) {
        try {
          // Parse the data field which contains JSON string
          let reviewData;
          if (typeof reviewItem.data === "string") {
            reviewData = JSON.parse(reviewItem.data);
          } else {
            reviewData = reviewItem.data || reviewItem;
          }
          const asinData = await prisma.lexASIN.findFirst({
            where: { asin: reviewData.ASIN || asin, countryCode },
            select: { id: true, type: true },
          });
          // Determine if this is a client review based on ASIN type
          const isClient = asinData?.type === "CLIENT";

          // Prepare review data for database
          const reviewRecord = {
            asin: reviewData.ASIN || asin,
            jobId: reviewItem.asinId || null, // Use asinId from the response
            reviewContent: reviewData.reviewContent,
            reviewTitle: reviewData["Review Title"],
            reviewScore: reviewData.reviewScore
              ? parseFloat(reviewData.reviewScore)
              : null,
            reviewDate: reviewData.reviewDate
              ? new Date(reviewData.reviewDate)
              : null,
            reviewer: reviewData.reviewer,
            reviewerCountry: reviewData.reviewerCountry,
            reviewerID: reviewData.reviewerID,
            isVerified:
              reviewData.isVerified === "True" ||
              reviewData.isVerified === true,
            reviewLink: reviewData["Review URL"],
            reviewerLink: reviewData.reviewerLink,
            HelpfulCounts: reviewData.HelpfulCounts
              ? parseInt(reviewData.HelpfulCounts)
              : null,
            image1: reviewData.image1,
            image2: reviewData.image2,
            image3: reviewData.image3,
            image4: reviewData.image4,
            pageUrl: reviewData.pageUrl,
            reviewID: reviewData["Review ID"] || reviewItem.reviewId,
            sellerId: reviewData["Seller Name"] || null, // Extract seller info
            productTitle: reviewData.productTitle,
            productLink: reviewData.productLink,
            variant_0: reviewData.variant_0,
            variant_1: reviewData.variant_1,
            asinId: asinData?.id || null,
            status: "DATA_SCRAPED", // Mark as data scraped, ready for AI processing
            isClient: isClient, // Set isClient based on ASIN type
            run_frequency: 3, // Set run_frequency based on client status
          };

          // Use upsert to handle duplicates based on reviewID
          if (reviewRecord.reviewID) {
            const result = await prisma.lexReview.upsert({
              where: { reviewID: reviewRecord.reviewID },
              update: {
                ...reviewRecord,
                updatedAt: new Date(),
              },
              create: {
                ...reviewRecord,
                createdAt: new Date(),
                updatedAt: new Date(),
              },
            });

            if (result.createdAt.getTime() === result.updatedAt.getTime()) {
              savedCount++;
            } else {
              updatedCount++;
            }
          } else {
            console.warn(`⚠️ No reviewID found for review, skipping...`);
            continue;
          }
        } catch (reviewError) {
          console.error(
            "❌ Error saving individual review:",
            reviewError.message
          );
          continue; // Continue with other reviews
        }
      }

      console.log(
        `💾 ASIN ${asin}: Saved ${savedCount} new reviews, updated ${updatedCount} existing reviews`
      );
    } catch (error) {
      console.error(`❌ Error saving reviews for ASIN ${asin}:`, error);
      throw error;
    }
  }

  async checkAndRestartServerIfNeeded(data) {
    try {
      // Prevent multiple restart attempts
      if (this.workerManager.getServerRestarting()) {
        console.log("🔄 Server restart already initiated, skipping check...");
        return;
      }

      console.log("🔍 Checking if all ASINs are processed...");

      // Check if there are any pending ASINs in the external API

      // console.log(`Response from get_reviews API:`, response.data);

      if (data && Array.isArray(data)) {
        const pendingAsins = data.filter(
          (asinResult) =>
            asinResult.status === "PENDING" ||
            asinResult.status === "PROCESSING" ||
            asinResult.status === "IN_PROGRESS"
        );

        const hasCookieExpired = pendingAsins.some(
          (asinResult) =>
            asinResult.status === "COOKIE_EXPIRED" ||
            asinResult.status === "FAILED"
        );

        const expiredCookiesIds = new Set(
          pendingAsins
            .filter(
              (asinResult) =>
                asinResult.status === "COOKIE_EXPIRED" ||
                asinResult.status === "FAILED"
            )
            .map((expiredCookiesId) => expiredCookiesId.cookieId)
        );

        console.log(
          `📊 Found ${pendingAsins.length} pending ASINs out of ${data.length} total`
        );

        if (
          (pendingAsins.length === 0 && data.length > 0) ||
          hasCookieExpired
        ) {
          console.log(
            "🎉 All ASINs processed or cookie expired! Initiating server restart..."
          );

          // Set global server restarting flag to prevent race conditions
          if (this.workerManager) {
            this.workerManager.setServerRestarting(true);
            console.log(
              "🔄 Set global serverRestarting flag to prevent job creation race condition"
            );
          }

          // Add a delay before cleanup to ensure all data is saved
          await this.delay(5000);

          try {
            // First clear the database
            console.log("�️ Calling clear_db API...");

            if (hasCookieExpired) {
              await this.markCookiesAsExpired(expiredCookiesIds);
            }

            await this.clearDatabase();
            console.log("✅ Database cleared successfully");

            // Then restart the server with throttling and state checking
            console.log("�🔄 Calling Azure server restart with throttling...");
            const restartReason = hasCookieExpired
              ? "Cookie expired - automatic restart"
              : "All ASINs processed - automatic restart";

            const restartResult = await restartServerSafely({
              reason: restartReason,
              force: false, // Don't force, respect throttling
            });

            if (restartResult.success) {
              console.log("✅ Server restart initiated successfully");
            } else if (restartResult.skipped) {
              console.log(
                `⏭️ Server restart skipped: ${restartResult.message}`
              );
            } else {
              console.error(
                `❌ Server restart failed: ${restartResult.message}`
              );
            }
            if (this.workerManager) {
              this.workerManager.setServerRestarting(false);
              console.log("🔄 Reset global serverRestarting finished");
            }
          } catch (error) {
            console.error("❌ Error during cleanup and restart:", error);

            if (this.workerManager) {
              this.workerManager.setServerRestarting(false);
              console.log("🔄 Reset global serverRestarting flag due to error");
            }
          }
        } else if (data.length === 0) {
          console.log("📭 No ASINs found in API response");
        } else {
          console.log(
            `⏳ Still waiting for ${pendingAsins.length} ASINs to complete processing`
          );
        }
      } else {
        console.log("📭 No valid response data from API");
      }
    } catch (error) {
      console.error("❌ Error checking ASIN status for server restart:", error);
      // Don't throw error to prevent worker from crashing
    }
  }

  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Get current restart status and throttling information
   * @returns {Object} Restart status information
   */
  getRestartStatus() {
    const status = getRestartStatus();
    console.log("📊 Current restart status:", status);
    return status;
  }

  async stop() {
    console.log("🛑 Stopping Lex Review Fetch Worker...");
    // Graceful shutdown logic here
  }
}

// Initialize and start worker if this file is run directly
if (require.main === module) {
  const worker = new LexReviewFetchWorker();
  worker.start();

  // Graceful shutdown
  process.on("SIGINT", async () => {
    await worker.stop();
    await prisma.$disconnect();
    process.exit(0);
  });
}

module.exports = LexReviewFetchWorker;
