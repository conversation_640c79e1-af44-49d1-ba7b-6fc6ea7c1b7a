const cron = require("node-cron");
const { PrismaClient } = require("@prisma/client");
const axios = require("axios");
const { sendMessageToSlack } = require("../utils/slack");
const { LEX_TAGGED_USERS } = require("../services/scrapeAmazon/constant");

const prisma = new PrismaClient();

// Tagged users for Slack notifications

class LexJobCreationWorker {
  constructor(workerManager = null) {
    this.BASE_URL = process.env.LEX_SCRAPER_BASE_URL || "http://localhost:3001";
    this.MAX_ASINS_PER_BATCH = 3;
    this.isRunning = false;

    // Throttling for notifications (5 minutes = 300000ms)
    this.NOTIFICATION_THROTTLE_MS = 5 * 60 * 1000;
    this.lastNotificationTimes = {
      allCookiesExpired: new Map(), // Map of countryCode -> timestamp
      criticalShortage: new Map(), // Map of countryCode -> timestamp
      individualExpired: new Map(), // Map of cookieId -> timestamp
    };

    this.workerManager = workerManager; // Reference to the worker manager for global state
  }
  async getProgressAsins() {
    const pendingAsinsFromTable = await prisma.lexASIN.findMany({
      where: {
        status: "REVIEW_IN_PROGRESS",
      },
      select: {
        asin: true,
        countryCode: true,
      },
      take: 10, // Limit to prevent overwhelming the system
    });
    return pendingAsinsFromTable;
  }

  async resetProgressAsins(asins) {
    await prisma.lexASIN.updateMany({
      where: {
        asin: { in: asins },
        status: "REVIEW_IN_PROGRESS",
      },
      data: {
        status: "REVIEW_PENDING",
        updatedAt: new Date(),
      },
    });
  }
  async start() {
    console.log("🚀 Starting Lex Job Creation Worker...");

    // Run every 3 minutes
    cron.schedule("*/2 * * * *", async () => {
      if (this.isRunning) {
        console.log("⏳ Job creation worker already running, skipping...");
        return;
      }

      try {
        this.isRunning = true;
        await this.processJobs();
      } catch (error) {
        console.error("❌ Error in job creation worker:", error);
      } finally {
        this.isRunning = false;
      }
    });

    console.log("✅ Lex Job Creation Worker scheduled to run every 2 minutes");
  }

  async processJobs() {
    console.log("🔄 Processing job creation...");

    try {
      // Check if server is restarting - skip job creation to prevent race condition
      if (this.workerManager && this.workerManager.getServerRestarting()) {
        console.log(
          "🔄 Server is restarting, skipping job creation to prevent race condition..."
        );
        return;
      }

      const existingJobsInQueue = await this.getProgressAsins();
      const existingJobsInAPI = await this.hasExistingASINsInProgress();

      if (existingJobsInQueue.length > 0 || existingJobsInAPI) {
        console.log("⏳ Still waiting for existing review jobs to complete...");
        return;
      }

      const { pendingAsins, countryCode } = await this.getPendingAsins();

      if (!pendingAsins || pendingAsins?.length === 0) {
        console.log("📭 No pending ASINs found");
        return;
      }

      // Get active cookies
      const { cookies, cookie_id } = await this.getActiveCookies(countryCode);

      if (cookies?.length === 0) {
        console.log(
          "🍪 No active cookies found - all may be expired or none available"
        );

        // Check if this is due to all cookies being expired vs no cookies at all
        const totalCookiesForCountry =
          await prisma.lexReviewScraperCookies.count({
            where: {
              countryCode: countryCode,
            },
          });

        if (totalCookiesForCountry === 0) {
          console.log(`📭 No cookies exist for country ${countryCode}`);
        } else {
          console.log(
            `⚠️ All ${totalCookiesForCountry} cookies for country ${countryCode} appear to be expired or inactive`
          );
        }

        return;
      }

      const batches = this.createBatches(
        pendingAsins,
        this.MAX_ASINS_PER_BATCH
      );

      const batch = batches[0];

      // Update each ASIN individually using the compound unique key
      for (const asin of batch) {
        try {
          await prisma.lexASIN.update({
            where: {
              asin_countryCode: {
                asin: asin,
                countryCode: countryCode,
              },
            },
            data: {
              status: "REVIEW_IN_PROGRESS",
              updatedAt: new Date(),
            },
          });
        } catch (updateError) {
          console.error(
            `❌ Failed to update ASIN ${asin} (${countryCode}):`,
            updateError.message
          );
        }
      }

      await this.createJobForBatch(batch, cookies, countryCode, cookie_id);

      // Small delay between batches
      await this.delay(1000);
    } catch (error) {
      console.error("❌ Error processing jobs:", error);
    }
  }

  async getPendingAsins() {
    try {
      // Get ASINs from jobs that are PENDING

      const pendingAsinsFromTable = await prisma.lexASIN.findMany({
        where: {
          status: "REVIEW_PENDING",
        },
        select: {
          asin: true,
          countryCode: true,
        },
        take: 20, // Limit to prevent overwhelming the system
      });

      // Group ASINs by country code to handle duplicates properly
      const asinsByCountry = new Map();
      for (const asinObj of pendingAsinsFromTable) {
        const countryCode = asinObj.countryCode || "US";
        const key = `${countryCode}`;
        if (!asinsByCountry.has(key)) {
          asinsByCountry.set(key, [
            {
              asin: asinObj.asin,
              countryCode: countryCode,
            },
          ]);
        } else {
          asinsByCountry.get(key).push({
            asin: asinObj.asin,
            countryCode: countryCode,
          });
        }
      }
      const asinEntries = Array.from(asinsByCountry.keys());
      const randomEntry =
        asinEntries[Math.floor(Math.random() * (asinEntries.length - 1))];
      // console.log(
      //   pendingAsinsFromTable,
      //   {
      //     pendingAsins: asinsByCountry.get(randomEntry),
      //     countryCode: randomEntry,
      //   },
      //   asinsByCountry,
      //   asinEntries,
      //   randomEntry
      // );
      return {
        pendingAsins: asinsByCountry
          .get(randomEntry)
          ?.map((asinObj) => asinObj.asin),

        countryCode: randomEntry,
      };
    } catch (error) {
      console.error("❌ Error getting pending ASINs:", error);
      return [];
    }
  }

  async getActiveCookies(countryCode = "US") {
    try {
      let offset = 0;
      const batchSize = 10; // Process cookies in batches
      let expiredCookieIds = [];

      while (true) {
        // Get next batch of cookies for the specific country, ordered by least recently used
        const cookies = await prisma.lexReviewScraperCookies.findMany({
          where: {
            active: true,
            cookieStatus: "ACTIVE",
            countryCode: countryCode,
          },
          orderBy: {
            lastUsed: {
              sort: "asc",
              nulls: "first",
            },
          },
          skip: offset,
          take: batchSize,
        });

        if (cookies.length === 0) {
          // No more cookies to check
          break;
        }

        // Check each cookie in this batch for expiration
        for (const selectedCookie of cookies) {
          console.log(
            `🍪 Checking cookie ID: ${selectedCookie.id} (${
              selectedCookie.emailId
            }, last used: ${selectedCookie.lastUsed || "never"})`
          );

          // Check if this cookie is expired
          const isExpired = this.checkCookieExpiration(
            selectedCookie.cookieKey
          );

          if (isExpired) {
            console.log(
              `🍪 Cookie ${selectedCookie.id} is expired, marking for expiration and trying next cookie`
            );

            // Add to expired list (we'll mark them all at once later)
            expiredCookieIds.push(selectedCookie.id);
            continue;
          }

          // Found a valid cookie! Mark any expired ones we found and return this one
          if (expiredCookieIds.length > 0) {
            console.log(
              `🍪 Marking ${expiredCookieIds.length} expired cookies before using valid cookie ${selectedCookie.id}`
            );
            await this.markCookiesAsExpired(expiredCookieIds);
          }

          console.log(
            `🍪 Selected valid cookie ID: ${selectedCookie.id} for country ${countryCode}`
          );

          // Don't mark as used yet - wait until job is successfully sent to API
          return {
            cookies: JSON.parse(selectedCookie.cookieKey),
            cookie_id: selectedCookie.id,
          };
        }

        // Move to next batch
        offset += batchSize;
      }

      // If we get here, we've checked all cookies and none were valid
      if (expiredCookieIds.length > 0) {
        console.log(`🍪 Marking ${expiredCookieIds.length} expired cookies`);
        await this.markCookiesAsExpired(expiredCookieIds);
      }

      console.log(
        `🍪 All available cookies for country ${countryCode} are expired or none exist`
      );

      // Send a critical Slack notification about all cookies being expired (throttled)
      if (this.shouldSendNotification("allCookiesExpired", countryCode)) {
        await sendMessageToSlack(
          process.env.LEX_NOTI_SLACK_WEBHOOK_URL,
          `🚨 CRITICAL: All cookies for country ${countryCode} are expired! No valid cookies available for scraping. Please add new cookies immediately.  ${LEX_TAGGED_USERS.join(
            " "
          )}`
        );
      }

      return {
        cookies: [],
        cookie_id: -1,
      };
    } catch (error) {
      console.error("❌ Error getting active cookies:", error);
      return {
        cookies: [],
        cookie_id: -1,
      };
    }
  }

  async markCookieAsUsed(cookieId) {
    try {
      await prisma.lexReviewScraperCookies.update({
        where: { id: cookieId },
        data: {
          lastUsed: new Date(),
          updatedAt: new Date(),
        },
      });
      console.log(
        `🍪 Marked cookie ${cookieId} as used at ${new Date().toISOString()}`
      );
    } catch (error) {
      console.error(`❌ Error marking cookie ${cookieId} as used:`, error);
    }
  }

  /**
   * Check if enough time has passed to send a notification (throttling)
   */
  shouldSendNotification(notificationType, key) {
    const now = Date.now();
    const lastSent = this.lastNotificationTimes[notificationType].get(key);

    if (!lastSent || now - lastSent >= this.NOTIFICATION_THROTTLE_MS) {
      this.lastNotificationTimes[notificationType].set(key, now);
      return true;
    }

    const timeRemaining = Math.ceil(
      (this.NOTIFICATION_THROTTLE_MS - (now - lastSent)) / 1000 / 60
    );
    console.log(
      `🔇 Throttling ${notificationType} notification for ${key} (${timeRemaining} min remaining)`
    );
    return false;
  }

  /**
   * Check if cookies in the cookieKey JSON array are expired
   * Cookies must be valid for at least 7 days from now
   */
  checkCookieExpiration(cookieKeyJson) {
    try {
      const cookieArray =
        typeof cookieKeyJson === "string"
          ? JSON.parse(cookieKeyJson)
          : cookieKeyJson;
      if (!Array.isArray(cookieArray)) {
        return false;
      }

      const currentTime = Date.now(); // Current time in milliseconds
      const sevenDaysInMs = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

      // Check if any cookie has expired after adding 7 days buffer
      for (const cookie of cookieArray) {
        if (cookie.expirationDate) {
          // Convert cookie expiration from seconds to milliseconds and add 7 days
          const cookieExpirationMs = cookie.expirationDate * 1000;
          const cookieExpirationWith7Days = cookieExpirationMs + sevenDaysInMs;

          if (cookieExpirationWith7Days < currentTime) {
            console.log(
              `🍪 Cookie ${
                cookie.name || "unknown"
              } expired (with 7-day buffer) - Original expiry: ${new Date(
                cookieExpirationMs
              ).toISOString()}, Extended expiry: ${new Date(
                cookieExpirationWith7Days
              ).toISOString()}`
            );
            return true;
          }
        }
      }

      console.log(`🍪 All cookies are valid with 7-day buffer`);
      return false;
    } catch (error) {
      console.error("❌ Error checking cookie expiration:", error);
      return false; // Assume not expired if we can't parse
    }
  }

  /**
   * Mark cookies as expired and send Slack notification
   */
  async markCookiesAsExpired(expiredCookieIds) {
    try {
      const expiredOnesList = Array.isArray(expiredCookieIds)
        ? expiredCookieIds
        : [expiredCookieIds];

      await prisma.lexReviewScraperCookies.updateMany({
        where: { id: { in: expiredOnesList } },
        data: {
          cookieStatus: "EXPIRE",
          active: false,
          updatedAt: new Date(),
        },
      });

      const expiredCookies = await prisma.lexReviewScraperCookies.findMany({
        where: { id: { in: expiredOnesList } },
        select: { id: true, emailId: true, countryCode: true },
      });

      // Send individual cookie expiration notifications (throttled per cookie)
      for (const cookie of expiredCookies) {
        if (
          this.shouldSendNotification("individualExpired", cookie.id.toString())
        ) {
          await sendMessageToSlack(
            process.env.LEX_NOTI_SLACK_WEBHOOK_URL,
            `Lex Cookie expired: ${cookie.emailId} ${
              cookie.countryCode || ""
            } (ID: ${cookie.id})  ${LEX_TAGGED_USERS.join(" ")}`
          );
        }
      }

      console.log(
        `🍪 Marked ${expiredOnesList.length} cookies as expired and sent Slack notification`
      );

      // Check if this was the last active cookie for any country
      await this.checkForCriticalCookieShortage();
    } catch (error) {
      console.error("❌ Error marking cookies as expired:", error);
    }
  }

  /**
   * Check if any country has run out of active cookies and send critical alerts
   */
  async checkForCriticalCookieShortage() {
    try {
      // Get count of active cookies by country
      const activeCookiesByCountry =
        await prisma.lexReviewScraperCookies.groupBy({
          by: ["countryCode"],
          where: {
            active: true,
            cookieStatus: "ACTIVE",
          },
          _count: { id: true },
        });

      // Get all countries that have pending ASINs
      const countriesWithPendingAsins = await prisma.lexASIN.groupBy({
        by: ["countryCode"],
        where: {
          status: "REVIEW_PENDING",
        },
        _count: { id: true },
      });

      // Check for countries with pending work but no active cookies
      for (const countryWithWork of countriesWithPendingAsins) {
        const activeCookiesForCountry = activeCookiesByCountry.find(
          (c) => c.countryCode === countryWithWork.countryCode
        );

        if (
          !activeCookiesForCountry ||
          activeCookiesForCountry._count.id === 0
        ) {
          // Send critical shortage notification (throttled per country)
          if (
            this.shouldSendNotification(
              "criticalShortage",
              countryWithWork.countryCode
            )
          ) {
            await sendMessageToSlack(
              process.env.LEX_NOTI_SLACK_WEBHOOK_URL,
              `🚨 CRITICAL:  Country ${countryWithWork.countryCode} has ${
                countryWithWork._count.id
              } pending ASINs but NO active cookies! Scraping will be blocked until new cookies are added. ${LEX_TAGGED_USERS.join(
                " "
              )}`
            );
          }

          console.log(
            `🚨 CRITICAL: No active cookies for ${countryWithWork.countryCode} with ${countryWithWork._count.id} pending ASINs`
          );
        }
      }
    } catch (error) {
      console.error("❌ Error checking cookie shortage:", error);
    }
  }

  createBatches(asins, batchSize) {
    const batches = [];
    for (let i = 0; i < asins.length; i += batchSize) {
      batches.push(asins.slice(i, i + batchSize));
    }
    return batches;
  }
  async getReviewsFromAPI() {
    try {
      console.log("🔍 Checking for existing ASINs in external API...");

      const response = await axios.get(`${this.BASE_URL}/api/get_reviews`, {
        headers: {
          "Content-Type": "application/json",
        },
        timeout: 60000, // 60 second timeout for review fetching
      });

      if (response.data && Array.isArray(response.data)) {
        console.log(`📥 Found ${response.data.length} ASINs in external API`);
        return response.data;
      } else {
        console.log("📭 No ASIN data found in external API response");
        return [];
      }
    } catch (error) {
      console.error(
        "❌ Error getting reviews from external API:",
        error.message
      );
      return [];
    }
  }

  /**
   * Check if there are any ASINs currently being processed in the external API
   */
  async hasExistingASINsInProgress() {
    try {
      const asinResults = await this.getReviewsFromAPI();

      if (!asinResults || asinResults.length === 0) {
        console.log(
          "✅ No ASINs found in external API - safe to create new jobs"
        );
        return false;
      }
    } catch (error) {
      console.error(
        "❌ Error getting reviews from external API:",
        error.message
      );
      return false;
    }
    return true;
  }

  async createJobForBatch(asins, cookies, countryCode = "US", cookieId) {
    try {
      const payload = {
        asins: asins,
        cookieKey: cookies,
        countryCode,
        cookieId,
      };
      // console.log(payload);
      console.log(`📤 Creating job for ASINs: ${asins.join(", ")}`);

      const response = await axios.post(
        `${this.BASE_URL}/api/create_job`,
        payload,
        {
          headers: {
            "Content-Type": "application/json",
          },
          timeout: 30000, // 30 second timeout
        }
      );

      if (response.data && response.data.message) {
        console.log(
          `✅ Job created successfully for ASINs: ${asins.join(", ")}`
        );

        // Mark cookie as used only after successful API call
        await this.markCookieAsUsed(cookieId);

        // Send Slack notification for successful job creation
        await sendMessageToSlack(
          process.env.LEX_NOTI_SLACK_WEBHOOK_URL,
          `✅ Lex job creation completed successfully!\n📦 ASINs: ${asins.join(
            ", "
          )}\n🌍 Country: ${countryCode}\n🍪 Cookie ID: ${cookieId}`
        );

        // Update job status to IN_PROGRESS
        // await this.updateJobStatus(asins, "IN_PROGRESS");
      } else {
        console.log(`⚠️ Job creation failed: ${JSON.stringify(response.data)}`);
      }
    } catch (error) {
      console.error(
        `❌ Error creating job for ASINs ${asins.join(", ")}:`,
        error.message
      );
      await this.resetProgressAsins(asins);

      // Note: Cookie is NOT marked as used if API call fails
      // This allows the same cookie to be retried later

      // Update job status to FAILED
      // await this.updateJobStatus(asins, "FAILED", error.message);
    }
  }

  async updateJobStatus(asins, status, errorMessage = null) {
    try {
      for (const asin of asins) {
        await prisma.lexJob.updateMany({
          where: {
            asin: asin,
            status: "PENDING",
          },
          data: {
            status: status,
            errorMessage: errorMessage,
          },
        });
      }
    } catch (error) {
      console.error("❌ Error updating job status:", error);
    }
  }

  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async stop() {
    console.log("🛑 Stopping Lex Job Creation Worker...");
    // Graceful shutdown logic here
  }
}

// Initialize and start worker if this file is run directly
if (require.main === module) {
  const worker = new LexJobCreationWorker();
  worker.start();

  // Graceful shutdown
  process.on("SIGINT", async () => {
    await worker.stop();
    await prisma.$disconnect();
    process.exit(0);
  });
}

module.exports = LexJobCreationWorker;
