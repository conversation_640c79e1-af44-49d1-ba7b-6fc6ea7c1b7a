const prisma = require("../../database/prisma/getPrismaClient");
const getAuditedJson = require("../../services/amazonAudit");
const { completionFactory } = require("../../services/scrapeGPT/factory");
const getAuditMailData = require("../amazonAuditUtils/getAuditMailData");
const { generateSlug } = require("../generateSlug");
const { generateAuditPdf, generateAuditScreenshots } = require("../getAmazonAudit");
const { getClientName } = require("../utils");
const createAndUpdateUTMUrl = require("../utmHelpers/createAndUpdateUTMUrl");

async function processAuditData(csvData, amazonData, clientId, auditPdf) {
  try {
    function onlyNumbers(str) {
      // Str "Assifianflojfbalfk 3, 89, 9, 10"
      // Output [3, 89, 9, 10]
      let arr = [];
      let num = 0;
      let inNumber = false;

      for (let i = 0; i < str.length; i++) {
        if (str[i] >= "0" && str[i] <= "9") {
          num = num * 10 + parseInt(str[i]);
          inNumber = true;
        } else {
          if (inNumber) {
            arr.push(num.toString());
            num = 0;
            inNumber = false;
          }
        }
      }

      // Push the last number if the string ends with a digit
      if (inNumber) {
        arr.push(num.toString());
      }

      return arr;
    }
    console.log(
      "Processing Audit Data for company:-----------------",
      csvData.companyName
    );
    // console.log("CSV Data:", csvData);
    // console.log("Amazon Data:", amazonData);

    let result;

    if (amazonData.data) {
      if (csvData.prospectDetails) {
        const asin = csvData.prospectDetails.asin;
        console.log(`ProspectDetails ASIN: ${asin}`);
        result = amazonData.data.productData.find((item) => item.asin === asin);
        if (result) {
          console.log("Found ASIN in amazonData");
          // console.log(`Found ASIN in amazonData: ${JSON.stringify(result)}`);
        } else {
          console.log("ASIN not found in amazonData");
        }
        const humanizedProspectProductTitle =
          csvData.prospectDetails.humanizedProspectProductTitle;
        if (!humanizedProspectProductTitle) {
          console.log("Humanised Title not Present, creating one.");
            const productName =
              amazonData?.data?.productData[0]?.title;
            let humanizedProspectProductTitleResponse;
            humanizedProspectProductTitleResponse = await completionFactory(
              "productTitleHumanisation",
              productName,
              clientId
            );
            csvData["promptTokens"] +=
              humanizedProspectProductTitleResponse?.prompt_tokens;
            csvData["completionTokens"] +=
              humanizedProspectProductTitleResponse?.completion_tokens;
          csvData.prospectDetails.humanizedProspectProductTitle =
            humanizedProspectProductTitleResponse?.message || productName;
        }
      }

      if (!result && amazonData.data.productData.length > 0) {
        result = amazonData.data.productData[0];
        console.log(
          `Using first ASIN from amazonData: ${JSON.stringify(result)}`
        );
      }

      console.log(
        "Generating Audit Data for company:-----------------",
        csvData.companyName
      );
      const transformedProductDetails = {
        company_name: amazonData.data.company_name,
        store: amazonData.data.store,
        productData: [result],
      };
      // console.log("Transformed Product Details:", transformedProductDetails);
      csvData["amazonAudit"] = transformedProductDetails;
      csvData["auditReport"] = await getAuditedJson(
        transformedProductDetails,
        clientId,
        true
      );

      const asin =
        csvData.prospectDetails.asin || amazonData.data.productData[0]?.asin;

      const slug = generateSlug(
        amazonData.data.company_name +
          " " +
          asin +
          " " +
          clientId
      );
      csvData["productSlug"] = slug;
      csvData["companySlug"] = generateSlug(amazonData.data.company_name);

      let productCategory = result.categoryAndRank;
      let productTitle = result.title;
      // Adding selected caseStudies

      let caseStudiesInput = { productCategory: "", productTitle: "" };
      if (productTitle) {
        caseStudiesInput.productTitle = productTitle;
      }
      if (Array.isArray(productCategory) && productCategory.length > 0) {
        csvData["category"] = productCategory;
        const element = productCategory[0];
        if (element && element.category) {
          caseStudiesInput.productCategory = element.category;
        } else {
          caseStudiesInput.productCategory = element;
        }
      } else {
        caseStudiesInput.productCategory = "N/A";
        csvData["category"] = ["N/A"];
      }

      if (caseStudiesInput.productCategory == "N/A") {
        console.log("BSR Category not found on amazon, trying with GPT...");
        const gptCategoryReponse = await completionFactory(
          "bsrCategoryUsingGpt",
          productTitle,
          clientId
        );

        caseStudiesInput.productCategory = gptCategoryReponse.message;
        csvData["category"] = [{ "category": gptCategoryReponse.message  }];
      }
      // console.log("Category:", csvData["category"]);
      // console.log("Input Values For CaseStudies Prompt:", caseStudiesInput);

      const caseStudiesResponse = await completionFactory(
        "caseStudies",
        caseStudiesInput,
        clientId
      );
      // console.log("Case Studies Response:", caseStudiesResponse);
      csvData["caseStudies"] = onlyNumbers(caseStudiesResponse.message)
        .sort(() => Math.random() - 0.5)
        .slice(0, 2);
      csvData["promptTokens"] += caseStudiesResponse.prompt_tokens;
      csvData["completionTokens"] += caseStudiesResponse.completion_tokens;
      console.log("Case Studies:", csvData["caseStudies"]);
      const auditReport = await prisma.amazonAuditReport.findFirst({
        where: {
          slug: slug,
        },
        orderBy: {
          updatedAt: "desc",
        },
      });
      const clientName = await getClientName(clientId);
  
      if (!auditReport) {
        console.log("Creating new audit report entry for:", slug);
        const createdReport = await prisma.amazonAuditReport.create({
          data: {
            companyId: csvData.companyId,
            productUrl:
              csvData.prospectDetails.productAmazonURL ||
              csvData.prospectDetails.bestSellingProductURL,
            auditReport: csvData.auditReport,
            amazonAudit: csvData.amazonAudit,
            companyName: amazonData.data.company_name,
            slug: slug,
            competitorUrl: csvData.competitorDetails.productAmazonURL,
            category: JSON.stringify(productCategory),
            caseStudies: csvData["caseStudies"],
            prospectDetails: csvData.prospectDetails,
          },
        });
        // console.log({ createdReport });
      } else {
        console.log("Updating new audit entry for:", slug);
        const updatedReport = await prisma.amazonAuditReport.update({
          where: {
            id: auditReport.id,
          },
          data: {
            productUrl:
              csvData.prospectDetails.productAmazonURL ||
              csvData.prospectDetails.bestSellingProductURL,
            auditReport: csvData.auditReport,
            amazonAudit: csvData.amazonAudit,
            companyName: amazonData.data.company_name,
            slug: slug,
            competitorUrl: csvData.competitorDetails.productAmazonURL,
            category: JSON.stringify(productCategory),
            caseStudies: csvData["caseStudies"],
            prospectDetails: csvData.prospectDetails,
          },
        });
        // console.log({ updatedReport });
      }
      csvData["auditMailData"] = await getAuditMailData(
        csvData,
        clientId
      );
      // Call the Generate Audit Function to generate the PDF
      if (auditPdf) {
        console.log("Generating PDFs for company: ", slug);
        await generateAuditPdf(csvData);
      } else {
        console.log("Generating screenshots only for company: ", slug);
        await generateAuditScreenshots(csvData);
      }
    }
  } catch (error) {
    console.error("Error in processAuditData:", error);
  }
}

module.exports = processAuditData;
