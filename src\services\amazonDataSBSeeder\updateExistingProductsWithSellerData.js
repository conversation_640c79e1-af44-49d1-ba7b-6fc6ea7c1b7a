// Load environment variables from .env file
require("dotenv").config();

const { PrismaClient } = require("@prisma/client");
const {
    transformAmazonDataToProduct,
} = require("./amazonProductDataToProductSeeder");
const {
    targetDbConfig,
    migrationConfig,
    testConnection,
    checkTargetTable,
} = require("./amazonProductDataToProductConfig");

const prisma = new PrismaClient();

// Function to get target database connection
async function getTargetDbConnection() {
    const { Client } = require('pg');

    let client;

    // Use connection string if available, otherwise fallback to parsed config
    if (process.env.TARGET_DATABASE_URL) {
        client = new Client({
            connectionString: process.env.TARGET_DATABASE_URL,
            ssl: {
                rejectUnauthorized: false,
                require: true,
                ca: undefined
            }
        });
    } else {
        // Fallback to parsed configuration
        let freshTargetDbConfig = {
            host: 'localhost',
            port: 5432,
            database: 'target_database',
            username: 'username',
            password: 'password',
            ssl: false,
        };

        if (process.env.TARGET_DB_HOST) {
            freshTargetDbConfig = {
                host: process.env.TARGET_DB_HOST,
                port: parseInt(process.env.TARGET_DB_PORT) || 5432,
                database: process.env.TARGET_DB_NAME || 'target_database',
                username: process.env.TARGET_DB_USER || 'username',
                password: process.env.TARGET_DB_PASSWORD || 'password',
                ssl: process.env.TARGET_DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
            };
        }

        client = new Client(freshTargetDbConfig);
    }

    await client.connect();
    return client;
}

// Function to extract seller information from Company.storeFrontURL
function extractSellerInfoFromStorefrontUrl(storefrontUrl) {
    if (!storefrontUrl) {
        return { sellerId: null, marketplace: null, sellerUrl: null };
    }

    try {
        let sellerId = null;
        let marketplace = 'US';
        let sellerUrl = storefrontUrl; // Use the original URL from Company table

        // Extract seller ID from seller URL pattern
        // Pattern: https://www.amazon.com/sp?seller=AQDJMKS738XKM
        const sellerMatch = storefrontUrl.match(/seller=([A-Z0-9]+)/);
        if (sellerMatch) {
            sellerId = sellerMatch[1];
        }

        // Extract marketplace from URL (default to US if not found)
        if (storefrontUrl.includes('.co.uk')) marketplace = 'UK';
        else if (storefrontUrl.includes('.de')) marketplace = 'DE';
        else if (storefrontUrl.includes('.fr')) marketplace = 'FR';
        else if (storefrontUrl.includes('.it')) marketplace = 'IT';
        else if (storefrontUrl.includes('.es')) marketplace = 'ES';
        else if (storefrontUrl.includes('.ca')) marketplace = 'CA';

        return {
            sellerId,
            marketplace,
            sellerUrl
        };
    } catch (error) {
        console.warn('Error extracting seller info from URL:', error.message);
        return { sellerId: null, marketplace: null, sellerUrl: null };
    }
}

// Function to get existing products that need seller data updates
async function getProductsNeedingSellerDataUpdate() {
    const { Client } = require('pg');

    let client;

    // Use connection string if available, otherwise fallback to parsed config
    if (process.env.TARGET_DATABASE_URL) {
        client = new Client({
            connectionString: process.env.TARGET_DATABASE_URL,
            ssl: {
                rejectUnauthorized: false,
                require: true,
                ca: undefined
            }
        });
    } else {
        // Fallback to parsed configuration
        let freshTargetDbConfig = {
            host: 'localhost',
            port: 5432,
            database: 'target_database',
            username: 'username',
            password: 'password',
            ssl: false,
        };

        if (process.env.TARGET_DB_HOST) {
            freshTargetDbConfig = {
                host: process.env.TARGET_DB_HOST,
                port: parseInt(process.env.TARGET_DB_PORT) || 5432,
                database: process.env.TARGET_DB_NAME || 'target_database',
                username: process.env.TARGET_DB_USER || 'username',
                password: process.env.TARGET_DB_PASSWORD || 'password',
                ssl: process.env.TARGET_DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
            };
        }

        client = new Client(freshTargetDbConfig);
    }

    try {
        await client.connect();

        // Find products that have null or empty seller information
        const result = await client.query(`
            SELECT id, url, brand_name, "sellerId", marketplace, "sellerUrl"
            FROM products 
            WHERE "sellerId" IS NULL 
               OR "sellerId" = '' 
               OR marketplace IS NULL 
               OR marketplace = ''
               OR "sellerUrl" IS NULL 
               OR "sellerUrl" = ''
            ORDER BY id
        `);

        console.log(`Found ${result.rows.length} products needing seller data updates`);
        return result.rows;
    } catch (error) {
        console.error('Error fetching products needing updates:', error.message);
        return [];
    } finally {
        await client.end();
    }
}

// Function to batch find matching AmazonProductData for multiple products
async function findMatchingAmazonProductDataBatch(products) {
    try {
        // Extract all URLs and brand names for batch querying
        const urls = products.map(p => p.url).filter(url => url && url !== 'N/A');
        const brandNames = products.map(p => p.brand_name).filter(brand => brand);

        // Batch query by URLs first
        const urlMatches = await prisma.amazonProductData.findMany({
            where: {
                OR: urls.map(url => ({
                    data: {
                        path: ['productData', '0', 'url'],
                        equals: url
                    }
                }))
            },
            include: {
                company: {
                    select: { storeFrontURL: true }
                }
            }
        });

        // Create a map for quick lookup
        const urlToDataMap = new Map();
        urlMatches.forEach(data => {
            const url = data.data?.productData?.[0]?.url;
            if (url) {
                urlToDataMap.set(url, data);
            }
        });

        // For products not found by URL, try brand name matching
        const remainingProducts = products.filter(p => !urlToDataMap.has(p.url));

        if (remainingProducts.length > 0) {
            const brandMatches = await prisma.amazonProductData.findMany({
                where: {
                    OR: brandNames.map(brand => ({
                        data: {
                            path: ['company_name'],
                            equals: brand.toLowerCase()
                        }
                    }))
                },
                include: {
                    company: {
                        select: { storeFrontURL: true }
                    }
                }
            });

            // Create brand name map
            const brandToDataMap = new Map();
            brandMatches.forEach(data => {
                const brand = data.data?.company_name;
                if (brand) {
                    brandToDataMap.set(brand.toLowerCase(), data);
                }
            });

            // Match remaining products by brand name
            remainingProducts.forEach(product => {
                if (product.brand_name && brandToDataMap.has(product.brand_name.toLowerCase())) {
                    urlToDataMap.set(product.url, brandToDataMap.get(product.brand_name.toLowerCase()));
                }
            });
        }

        // Return results in the same order as input products
        return products.map(product => urlToDataMap.get(product.url) || null);
    } catch (error) {
        console.error('Error finding matching AmazonProductData batch:', error.message);
        return products.map(() => null);
    }
}

// Function to batch update products with seller data
async function updateProductsWithSellerDataBatch(targetDb, updates) {
    if (updates.length === 0) return { successCount: 0, errorCount: 0 };

    try {
        // Use a transaction for better performance and consistency
        await targetDb.query('BEGIN');

        const updateQuery = `
            UPDATE products 
            SET marketplace = $1, "sellerId" = $2, "sellerUrl" = $3, "updatedAt" = $4
            WHERE id = $5
        `;

        let successCount = 0;
        let errorCount = 0;

        // Process updates in parallel within the transaction
        const updatePromises = updates.map(async (update) => {
            try {
                const values = [
                    update.sellerData.marketplace,
                    update.sellerData.sellerId,
                    update.sellerData.sellerUrl,
                    new Date(),
                    update.productId
                ];

                const result = await targetDb.query(updateQuery, values);

                if (result.rowCount > 0) {
                    successCount++;
                    return { success: true, productId: update.productId };
                } else {
                    errorCount++;
                    return { success: false, productId: update.productId, error: 'No rows updated' };
                }
            } catch (error) {
                errorCount++;
                return { success: false, productId: update.productId, error: error.message };
            }
        });

        const results = await Promise.all(updatePromises);

        await targetDb.query('COMMIT');

        // Log results
        results.forEach(result => {
            if (result.success) {
                console.log(`✅ Updated product ID ${result.productId} with seller data`);
            } else {
                console.log(`❌ Error updating product ID ${result.productId}: ${result.error}`);
            }
        });

        return { successCount, errorCount };
    } catch (error) {
        await targetDb.query('ROLLBACK');
        console.error('❌ Batch update failed:', error.message);
        return { successCount: 0, errorCount: updates.length };
    }
}

// Main function to update existing products with seller data
async function updateExistingProductsWithSellerData() {
    let targetDb = null;

    try {
        console.log('=== Updating Existing Products with Seller Data ===\n');

        // Test connection and table existence
        console.log('1. Testing database connection...');
        const connectionOk = await testConnection();
        if (!connectionOk) {
            throw new Error('Failed to connect to target database');
        }
        console.log('✅ Database connection successful');

        console.log('\n2. Checking target table...');
        const tableExists = await checkTargetTable();
        if (!tableExists) {
            throw new Error('Target table "products" does not exist');
        }
        console.log('✅ Target table exists');

        // Get target database connection
        targetDb = await getTargetDbConnection();

        // Get products that need seller data updates
        console.log('\n3. Finding products needing seller data updates...');
        const productsToUpdate = await getProductsNeedingSellerDataUpdate();

        if (productsToUpdate.length === 0) {
            console.log('✅ No products need seller data updates');
            return;
        }

        console.log(`Found ${productsToUpdate.length} products to update`);

        let totalSuccessCount = 0;
        let totalErrorCount = 0;
        let totalNoMatchCount = 0;

        // Process products in larger batches for better performance
        const batchSize = Math.min(migrationConfig.batchSize * 2, 100); // Increased batch size
        const totalBatches = Math.ceil(productsToUpdate.length / batchSize);

        for (let i = 0; i < productsToUpdate.length; i += batchSize) {
            const batch = productsToUpdate.slice(i, i + batchSize);
            const batchNumber = Math.floor(i / batchSize) + 1;

            console.log(`\nProcessing batch ${batchNumber}/${totalBatches} (${batch.length} products)`);

            // Find matching AmazonProductData for the entire batch
            const amazonProductDataBatch = await findMatchingAmazonProductDataBatch(batch);

            // Prepare updates for products that have matches
            const updates = [];
            let batchNoMatchCount = 0;

            for (let j = 0; j < batch.length; j++) {
                const product = batch[j];
                const amazonProductData = amazonProductDataBatch[j];

                if (!amazonProductData) {
                    console.log(`⚠️  No matching AmazonProductData found for product ID ${product.id} (URL: ${product.url})`);
                    batchNoMatchCount++;
                    continue;
                }

                // Extract seller information from Company.storeFrontURL only
                let sellerInfo = { sellerId: null, marketplace: null, sellerUrl: null };

                if (amazonProductData.company && amazonProductData.company.storeFrontURL) {
                    sellerInfo = extractSellerInfoFromStorefrontUrl(amazonProductData.company.storeFrontURL);
                }

                updates.push({
                    productId: product.id,
                    sellerData: sellerInfo
                });
            }

            // Batch update the products
            if (updates.length > 0) {
                const { successCount, errorCount } = await updateProductsWithSellerDataBatch(targetDb, updates);
                totalSuccessCount += successCount;
                totalErrorCount += errorCount;
            }

            totalNoMatchCount += batchNoMatchCount;

            // Reduced delay between batches
            if (i + batchSize < productsToUpdate.length) {
                await new Promise(resolve => setTimeout(resolve, 500)); // Reduced from 1000ms to 500ms
            }
        }

        // Summary
        console.log('\n=== Update Summary ===');
        console.log(`✅ Successfully updated: ${totalSuccessCount} products`);
        console.log(`❌ Errors: ${totalErrorCount} products`);
        console.log(`⚠️  No matching data found: ${totalNoMatchCount} products`);
        console.log(`📊 Total processed: ${productsToUpdate.length} products`);

    } catch (error) {
        console.error('❌ Update failed:', error);
        throw error;
    } finally {
        if (targetDb) {
            await targetDb.end();
        }
        await prisma.$disconnect();
    }
}

// Function to test the update process with a single product
async function testUpdateWithSingleProduct(productId) {
    let targetDb = null;

    try {
        console.log(`=== Testing Update with Product ID ${productId} ===\n`);

        // Test connection
        const connectionOk = await testConnection();
        if (!connectionOk) {
            throw new Error('Failed to connect to target database');
        }

        // Get target database connection
        targetDb = await getTargetDbConnection();

        // Get the specific product
        const { Client } = require('pg');

        let client;

        // Use connection string if available, otherwise fallback to parsed config
        if (process.env.TARGET_DATABASE_URL) {
            client = new Client({
                connectionString: process.env.TARGET_DATABASE_URL,
                ssl: {
                    rejectUnauthorized: false,
                    require: true,
                    ca: undefined
                }
            });
        } else {
            // Fallback to parsed configuration
            let freshTargetDbConfig = {
                host: 'localhost',
                port: 5432,
                database: 'target_database',
                username: 'username',
                password: 'password',
                ssl: false,
            };

            if (process.env.TARGET_DB_HOST) {
                freshTargetDbConfig = {
                    host: process.env.TARGET_DB_HOST,
                    port: parseInt(process.env.TARGET_DB_PORT) || 5432,
                    database: process.env.TARGET_DB_NAME || 'target_database',
                    username: process.env.TARGET_DB_USER || 'username',
                    password: process.env.TARGET_DB_PASSWORD || 'password',
                    ssl: process.env.TARGET_DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
                };
            }

            client = new Client(freshTargetDbConfig);
        }

        await client.connect();

        const productResult = await client.query(`
            SELECT id, url, brand_name, "sellerId", marketplace, "sellerUrl"
            FROM products 
            WHERE id = $1
        `, [productId]);

        await client.end();

        if (productResult.rows.length === 0) {
            console.log(`❌ Product ID ${productId} not found`);
            return;
        }

        const product = productResult.rows[0];
        console.log('Current product data:', product);

        // Find matching AmazonProductData using batch function
        const [amazonProductData] = await findMatchingAmazonProductDataBatch([product]);

        if (!amazonProductData) {
            console.log(`❌ No matching AmazonProductData found for product ID ${productId}`);
            return;
        }

        console.log('Found matching AmazonProductData ID:', amazonProductData.id);

        // Extract seller information
        let sellerInfo = { sellerId: null, marketplace: null, sellerUrl: null };

        if (amazonProductData.company && amazonProductData.company.storeFrontURL) {
            sellerInfo = extractSellerInfoFromStorefrontUrl(amazonProductData.company.storeFrontURL);
        }

        console.log('Extracted seller info:', sellerInfo);

        // Update the product using batch function
        const { successCount } = await updateProductsWithSellerDataBatch(targetDb, [{
            productId: productId,
            sellerData: sellerInfo
        }]);

        if (successCount > 0) {
            console.log('✅ Test update successful');
        } else {
            console.log('❌ Test update failed');
        }

    } catch (error) {
        console.error('❌ Test failed:', error);
    } finally {
        if (targetDb) {
            await targetDb.end();
        }
        await prisma.$disconnect();
    }
}

// Main execution
async function main() {
    const args = process.argv.slice(2);

    if (args.length === 0) {
        console.log('Usage:');
        console.log('  node updateExistingProductsWithSellerData.js --update-all');
        console.log('  node updateExistingProductsWithSellerData.js --test <product_id>');
        return;
    }

    const command = args[0];

    if (command === '--update-all') {
        await updateExistingProductsWithSellerData();
    } else if (command === '--test' && args[1]) {
        const productId = parseInt(args[1]);
        await testUpdateWithSingleProduct(productId);
    } else {
        console.log('Invalid command. Use --update-all or --test <product_id>');
    }
}

// Export functions for testing
module.exports = {
    updateExistingProductsWithSellerData,
    testUpdateWithSingleProduct,
    getProductsNeedingSellerDataUpdate,
    findMatchingAmazonProductDataBatch,
    updateProductsWithSellerDataBatch
};

// Run if called directly
if (require.main === module) {
    main().catch(console.error);
} 