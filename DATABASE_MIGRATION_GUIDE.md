# Database Migration Guide: AWS RDS to Azure PostgreSQL

This guide provides step-by-step instructions for migrating the database from AWS RDS to Azure PostgreSQL and updating hardcoded AWS references.

## Prerequisites

1. Azure PostgreSQL Flexible Server instance created
2. Network access configured between Azure resources
3. Database backup from AWS RDS
4. Azure CLI installed and configured

## Migration Steps

### 1. Create Azure PostgreSQL Database

```bash
# Create Azure PostgreSQL Flexible Server
az postgres flexible-server create \
  --resource-group your-resource-group \
  --name your-postgres-server \
  --location "Central India" \
  --admin-user your-admin-user \
  --admin-password your-admin-password \
  --sku-name Standard_B2s \
  --tier Burstable \
  --storage-size 32 \
  --version 14

# Create the main database
az postgres flexible-server db create \
  --resource-group your-resource-group \
  --server-name your-postgres-server \
  --database-name jeff-core

# Create the sellers database (for target migrations)
az postgres flexible-server db create \
  --resource-group your-resource-group \
  --server-name your-postgres-server \
  --database-name sellers-db
```

### 2. Export Data from AWS RDS

```bash
# Create backup from AWS RDS
pg_dump -h your-aws-rds-endpoint \
        -U your-username \
        -d your-database \
        -f aws_backup.sql \
        --no-owner \
        --no-privileges \
        --clean \
        --if-exists

# For specific schema backup
pg_dump -h your-aws-rds-endpoint \
        -U your-username \
        -d your-database \
        -n public \
        -f aws_public_schema.sql \
        --no-owner \
        --no-privileges
```

### 3. Import Data to Azure PostgreSQL

```bash
# Import to Azure PostgreSQL
psql -h your-azure-postgres.postgres.database.azure.com \
     -U your-admin-user \
     -d jeff-core \
     -f aws_backup.sql

# Create jeff schema in sellers-db for target migrations
psql -h your-azure-postgres.postgres.database.azure.com \
     -U your-admin-user \
     -d sellers-db \
     -c "CREATE SCHEMA IF NOT EXISTS jeff;"
```

### 4. Update Connection Strings

Update your `.env` file with Azure PostgreSQL connection strings:

```bash
# Main database connection
DATABASE_URL=postgresql://your-admin-user:<EMAIL>:5432/jeff-core?sslmode=require

# Target database for migrations
TARGET_DATABASE_URL=postgresql://your-admin-user:<EMAIL>:5432/sellers-db?sslmode=require
```

## Hardcoded AWS References Update

### 1. AWS Instance IDs in Database

Create a script to find and update AWS instance IDs:

```sql
-- Find tables with potential AWS instance references
SELECT table_name, column_name 
FROM information_schema.columns 
WHERE column_name ILIKE '%instance%' 
   OR column_name ILIKE '%aws%'
   OR column_name ILIKE '%ec2%';

-- Example: Update instance IDs in configurations table
UPDATE "Configurations" 
SET "scraperApi" = jsonb_set(
    "scraperApi", 
    '{instanceIds}', 
    '["jeff-worker-1", "jeff-worker-2", "jeff-worker-3", "jeff-worker-4"]'::jsonb
)
WHERE "scraperApi" ? 'instanceIds';

-- Update any hardcoded AWS regions
UPDATE "Configurations" 
SET "scraperApi" = jsonb_set(
    "scraperApi", 
    '{region}', 
    '"Central India"'::jsonb
)
WHERE "scraperApi" ? 'region';
```

### 2. Configuration Tables Update

Update configuration tables to support Azure resource identifiers:

```sql
-- Add Azure-specific configuration fields
ALTER TABLE "Configurations" 
ADD COLUMN IF NOT EXISTS "azureConfig" JSONB DEFAULT '{}';

-- Update existing configurations with Azure settings
UPDATE "Configurations" 
SET "azureConfig" = jsonb_build_object(
    'resourceGroup', 'your-resource-group',
    'subscriptionId', 'your-subscription-id',
    'location', 'Central India',
    'availableVMs', '["jeff-worker-1", "jeff-worker-2", "jeff-worker-3", "jeff-worker-4"]'::jsonb,
    'storageAccount', 'your-storage-account',
    'containerName', 'eq-assets'
);
```

### 3. Update Available Instances Arrays

```sql
-- Find and update any tables storing instance arrays
-- This is an example - adjust based on your actual schema

-- Update job configurations
UPDATE "JobCentral" 
SET "config" = jsonb_set(
    COALESCE("config", '{}'::jsonb),
    '{availableInstances}',
    '["jeff-worker-1", "jeff-worker-2", "jeff-worker-3", "jeff-worker-4"]'::jsonb
)
WHERE "config" ? 'availableInstances';

-- Update any AWS-specific settings
UPDATE "JobCentral" 
SET "config" = "config" - 'awsRegion' || jsonb_build_object('azureLocation', 'Central India')
WHERE "config" ? 'awsRegion';
```

## Data Validation

### 1. Verify Data Integrity

```sql
-- Check row counts match
SELECT 'Users' as table_name, COUNT(*) as count FROM "User"
UNION ALL
SELECT 'Jobs', COUNT(*) FROM "JobCentral"
UNION ALL
SELECT 'Configurations', COUNT(*) FROM "Configurations";

-- Verify critical data
SELECT id, email, role FROM "User" WHERE role = 'ADMIN';
SELECT id, "clientId", "createdAt" FROM "Configurations" ORDER BY "createdAt" DESC LIMIT 5;
```

### 2. Test Application Connectivity

```bash
# Test Prisma connection
npx prisma db pull
npx prisma generate

# Run application tests
npm test

# Test database operations
node -e "
const prisma = require('./src/database/prisma/getPrismaClient');
prisma.user.findMany().then(users => {
  console.log('Users found:', users.length);
  process.exit(0);
}).catch(err => {
  console.error('Database error:', err);
  process.exit(1);
});
"
```

## Migration Scripts

### 1. AWS to Azure Instance ID Mapping Script

Create `scripts/migrate-instance-ids.js`:

```javascript
const prisma = require('../src/database/prisma/getPrismaClient');

const AWS_TO_AZURE_MAPPING = {
  'i-0eca89a83b7c830be': 'jeff-worker-1',
  'i-0b9f2d0b5678231ec': 'jeff-worker-2',
  'i-0d480a46ac9f608ac': 'jeff-worker-3',
  'i-0ce83b7b014f47885': 'jeff-worker-4'
};

async function migrateInstanceIds() {
  console.log('Starting instance ID migration...');
  
  // Update configurations
  const configs = await prisma.configurations.findMany();
  
  for (const config of configs) {
    let updated = false;
    let scraperApi = config.scraperApi;
    
    // Update instance IDs in scraperApi config
    if (scraperApi.instanceIds) {
      scraperApi.instanceIds = scraperApi.instanceIds.map(id => 
        AWS_TO_AZURE_MAPPING[id] || id
      );
      updated = true;
    }
    
    if (updated) {
      await prisma.configurations.update({
        where: { id: config.id },
        data: { scraperApi }
      });
      console.log(`Updated configuration ${config.id}`);
    }
  }
  
  console.log('Instance ID migration completed');
}

migrateInstanceIds().catch(console.error).finally(() => prisma.$disconnect());
```

### 2. Configuration Cleanup Script

Create `scripts/cleanup-aws-references.js`:

```javascript
const prisma = require('../src/database/prisma/getPrismaClient');

async function cleanupAwsReferences() {
  console.log('Cleaning up AWS references...');
  
  // Remove AWS-specific fields and add Azure equivalents
  const configs = await prisma.configurations.findMany();
  
  for (const config of configs) {
    const updates = {};
    
    // Clean up scraperApi config
    if (config.scraperApi) {
      let scraperApi = { ...config.scraperApi };
      
      // Remove AWS-specific fields
      delete scraperApi.awsRegion;
      delete scraperApi.awsAccessKey;
      delete scraperApi.awsSecretKey;
      
      // Add Azure equivalents
      scraperApi.azureLocation = 'Central India';
      scraperApi.resourceGroup = process.env.RESOURCE_GROUP_NAME;
      
      updates.scraperApi = scraperApi;
    }
    
    // Add Azure configuration
    updates.azureConfig = {
      resourceGroup: process.env.RESOURCE_GROUP_NAME,
      subscriptionId: process.env.AZURE_SUBSCRIPTION_ID,
      location: 'Central India',
      storageAccount: process.env.AZURE_STORAGE_ACCOUNT_NAME
    };
    
    await prisma.configurations.update({
      where: { id: config.id },
      data: updates
    });
    
    console.log(`Cleaned up configuration ${config.id}`);
  }
  
  console.log('AWS reference cleanup completed');
}

cleanupAwsReferences().catch(console.error).finally(() => prisma.$disconnect());
```

## Post-Migration Checklist

- [ ] Database connection successful
- [ ] All tables migrated with correct row counts
- [ ] AWS instance IDs updated to Azure VM names
- [ ] Configuration tables updated with Azure settings
- [ ] Application starts without database errors
- [ ] Auto-scaling worker uses Azure VMs
- [ ] Image upload uses Azure Blob Storage
- [ ] All critical functionality tested

## Rollback Plan

1. **Database Rollback**: Keep AWS RDS running during migration
2. **Connection String Rollback**: Switch back to AWS RDS connection
3. **Configuration Rollback**: Restore original configurations from backup

## Monitoring and Maintenance

1. Set up Azure Database monitoring
2. Configure automated backups
3. Monitor connection pool usage
4. Set up alerts for database performance
5. Regular maintenance windows for updates

## Security Considerations

1. Use Azure AD authentication where possible
2. Enable SSL/TLS for all connections
3. Configure firewall rules appropriately
4. Use Azure Key Vault for sensitive configuration
5. Regular security updates and patches
