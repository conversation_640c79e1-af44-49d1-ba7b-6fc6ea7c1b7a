// Singleton protection to prevent multiple scheduler executions
// ===== DEPENDENCIES =====
require("dotenv").config();
const LexWorkerManager = require("./lexWorkerManager");
const AutoScalingWorker = require("./autoScalingWorker");
const { sendServerNotification } = require("../utils/slack");
if (!global.schedulerInitialized) {
  global.schedulerInitialized = true;

  let lexWorkerManager = null;

  // ===== AMAZON PRODUCT DATA SYNC WORKER =====
  if (process.env.ENABLE_AMAZON_SYNC === "true") {
    const { initializeSyncJob } = require("./amazonProductDataSyncWorker");
    console.log("🔄 Initializing AmazonProductData sync worker...");
    initializeSyncJob();
  }

  // ===== AUTO SCALING WORKER (Azure-enabled) =====
  if (process.env.SERVER_ID === "Main") {
    console.log("🔄 Initializing Azure-enabled Auto Scaling Worker...");
    const autoScalingWorker = new AutoScalingWorker();
    autoScalingWorker.start();
  }

  // ===== LEX WORKER MANAGER =====
  if (process.env.SERVER_QUEUE === "lexQueue") {
    // Initialize Lex Worker Manager using singleton pattern
    if (!LexWorkerManager.hasInstance()) {
      console.log("🔄 Initializing Lex Worker Manager...");
      lexWorkerManager = LexWorkerManager.getInstance();
    } else {
      console.log("🔄 Using existing Lex Worker Manager instance...");
      lexWorkerManager = LexWorkerManager.getInstance();
    }

    // Initialize additional workers
    require("./lexWeeklyAsinChecker");
    // require("./lexReviewCheckerWorker"); // Daily worker (disabled)

    console.log("🚀 Starting Scheduler with Lex Worker Manager...");

    // Start the workers only if not already running
    if (!lexWorkerManager.isRunning) {
      lexWorkerManager.start().catch(console.error);
    } else {
      console.log(
        "⚠️ Lex Worker Manager is already running, skipping start..."
      );
    }
    // ===== GRACEFUL SHUTDOWN HANDLING =====
    const gracefulShutdown = async (signal) => {
      console.log(`\n🔄 Received ${signal}, initiating graceful shutdown...`);

      try {
        // Send server stop notification
        await sendServerNotification("STOP", `Received ${signal} signal`);
        await lexWorkerManager.stop();
        console.log("👋 Scheduler shutdown complete");
        process.exit(0);
      } catch (error) {
        console.error("❌ Error during scheduler shutdown:", error);
        process.exit(1);
      }
    };

    // Handle shutdown signals
    process.on("SIGINT", () => gracefulShutdown("SIGINT"));
    process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));

    // Handle uncaught exceptions
    process.on("uncaughtException", (error) => {
      console.error("💥 Uncaught Exception in scheduler:", error);
      gracefulShutdown("uncaughtException");
    });

    process.on("unhandledRejection", (reason, promise) => {
      console.error(
        "💥 Unhandled Rejection in scheduler at:",
        promise,
        "reason:",
        reason
      );
      gracefulShutdown("unhandledRejection");
    });
  }
  // ===== BULL WORKER =====
  require("./bullWorker");

  // ===== COMMENTED OUT WORKERS =====
  // const scheduleJobStatusUpdate = require("../worker/updateCSVData");
  // const reviewChecker = require("../worker/lexImageGenWorker");
  // const processLexJobs = require('./lexWorker')
  // const processLexReviewJobs = require('./lexReviewsWorker')
  // const processAiAnalysisJobs = require('./lexAiAnalysisWorker')
} else {
  console.log("🔄 Scheduler already initialized, skipping...");
}
