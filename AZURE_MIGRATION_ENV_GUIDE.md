# Azure Migration Environment Configuration Guide

This guide outlines the environment variable changes required for migrating from AWS to Azure infrastructure.

## Required Azure Environment Variables

### Azure Authentication
Add these Azure service principal credentials to your `.env` file:

```bash
# Azure Service Principal Credentials
AZURE_TENANT_ID=your-tenant-id
AZURE_CLIENT_ID=your-client-id
AZURE_CLIENT_SECRET=your-client-secret
AZURE_SUBSCRIPTION_ID=your-subscription-id

# Azure Resource Configuration
RESOURCE_GROUP_NAME=your-resource-group-name
AZURE_LOCATION=Central India
VM_NAME=your-main-vm-name
PUBLIC_IP_NAME=your-public-ip-name

# Azure Storage Configuration
AZURE_STORAGE_ACCOUNT_NAME=your-storage-account-name
AZURE_STORAGE_ACCOUNT_KEY=your-storage-account-key
AZURE_STORAGE_CONTAINER_NAME=eq-assets

# Azure Functions Configuration (Optional - for deployed functions)
AZURE_FUNCTION_URL=https://your-function-app.azurewebsites.net
AZURE_FUNCTION_KEY=your-function-key
```

## AWS to Azure Variable Mapping

### Authentication Credentials
| AWS Variable | Azure Equivalent | Notes |
|--------------|------------------|-------|
| `AWS_ACCESS_KEY_ID` | `AZURE_CLIENT_ID` | Service principal client ID |
| `AWS_SECRET_ACCESS_KEY` | `AZURE_CLIENT_SECRET` | Service principal secret |
| `AWS_REGION` | `AZURE_LOCATION` | Azure region (e.g., "Central India") |
| N/A | `AZURE_TENANT_ID` | Azure tenant ID (new requirement) |
| N/A | `AZURE_SUBSCRIPTION_ID` | Azure subscription ID (new requirement) |

### Resource Configuration
| AWS Variable | Azure Equivalent | Notes |
|--------------|------------------|-------|
| N/A | `RESOURCE_GROUP_NAME` | Azure resource group containing VMs |
| N/A | `VM_NAME` | Primary VM name for operations |
| N/A | `PUBLIC_IP_NAME` | Public IP resource name |

### Storage Configuration
| AWS Variable | Azure Equivalent | Notes |
|--------------|------------------|-------|
| S3 bucket name (hardcoded) | `AZURE_STORAGE_CONTAINER_NAME` | Container name in Azure Blob Storage |
| AWS credentials | `AZURE_STORAGE_ACCOUNT_NAME` | Storage account name |
| AWS credentials | `AZURE_STORAGE_ACCOUNT_KEY` | Storage account access key |

## Migration Steps

### 1. Keep AWS Variables (Temporary)
During migration, keep existing AWS variables for services that haven't been migrated yet:
```bash
# Keep these during migration for SES and other AWS services
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=ap-south-1
```

### 2. Add Azure Variables
Add all Azure variables listed above to your `.env` file.

### 3. Update Application Configuration
The application will automatically use Azure services where implemented and fall back to AWS for services not yet migrated.

## Service-Specific Configuration

### Auto Scaling Worker
Update the `availableInstances` array in `src/worker/autoScalingWorker.js`:
```javascript
availableInstances: ["jeff-worker-1", "jeff-worker-2", "jeff-worker-3", "jeff-worker-4"]
```
Replace with your actual Azure VM names.

### Database Configuration
Update database connection strings to point to Azure PostgreSQL:
```bash
# Azure PostgreSQL connection
DATABASE_URL=postgresql://username:<EMAIL>:5432/database_name?sslmode=require

# Target database for migrations
TARGET_DATABASE_URL=postgresql://username:<EMAIL>:5432/sellers-db?sslmode=require
```

### Scraper API Configuration
Update scraper API key if needed:
```bash
SCRAPER_API_KEY=your-scraper-api-key
```

## Verification Steps

### 1. Test Azure VM Management
```bash
# Test listing Azure VMs
curl -X GET "http://localhost:8000/api/azure/instances" \
  -H "Authorization: Bearer your-jwt-token"
```

### 2. Test Azure Blob Storage
```bash
# Test uploading to Azure Blob Storage
# This will be tested through the application's image upload functionality
```

### 3. Test Azure Functions
```bash
# Test Azure Function trigger
# This will be tested through the application's image generation functionality
```

## Rollback Plan

If issues occur during migration:

1. **Immediate Rollback**: Switch routes back to AWS endpoints
2. **Environment Rollback**: Remove Azure variables and ensure AWS variables are correct
3. **Code Rollback**: Use git to revert to pre-migration state

## Security Considerations

1. **Service Principal Permissions**: Ensure Azure service principal has minimal required permissions
2. **Storage Access**: Use storage account keys or SAS tokens appropriately
3. **Function Keys**: Secure Azure Function keys properly
4. **Environment Variables**: Never commit `.env` files to version control

## Monitoring and Logging

After migration:
1. Monitor Azure VM scaling operations
2. Check Azure Blob Storage upload/download operations
3. Monitor Azure Function execution logs
4. Set up Azure alerts for resource usage

## Support and Troubleshooting

### Common Issues
1. **Authentication Errors**: Verify service principal credentials
2. **Resource Not Found**: Check resource group and resource names
3. **Permission Denied**: Verify service principal has required permissions
4. **Storage Errors**: Check storage account name and key

### Useful Azure CLI Commands
```bash
# Login to Azure
az login

# List resource groups
az group list

# List VMs in resource group
az vm list --resource-group your-resource-group

# List storage accounts
az storage account list
```

## Next Steps After Environment Setup

1. Update any hardcoded AWS instance IDs in database
2. Test all Azure integrations thoroughly
3. Update monitoring and alerting systems
4. Train team on Azure-specific operations
5. Update deployment scripts and CI/CD pipelines
