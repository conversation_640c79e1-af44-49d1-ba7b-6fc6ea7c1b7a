const prisma = require("../database/prisma/getPrismaClient");
const cron = require("node-cron");
const Sentry = require("@sentry/node");

const { scrapeData } = require("../utils/scriptRunner");
const { sendErrorEmail } = require("../utils/mailHelper");
const { generateCompEmail } = require("../utils/emailUtils/generateEmail");
const { getFromCache, cache, clearCache } = require("../services/cache/store");

const processFinalData = require("../utils/workerUtils/processFinalData");
const processAmazonData = require("../utils/workerUtils/processAmazonData");
const processCompetitionData = require("../utils/workerUtils/processCompetitionData");
const processPPCAudit = require("../utils/workerUtils/processPPCAudit");
const processAuditData = require("../utils/workerUtils/processAuditData");
const { closeBrowsers } = require("../utils/puppeteer/browserHelper");
const reloadServer = require("./reloadServer");
const {
  getTotalPrice,
  resetPricing,
} = require("../utils/pricing/pricingCalculator");

// Import the encoded audit slug utility
const { createEncodedURL } = require("../utils/encodedAuditSlug");

let reprocessingMap = {};
// cron.schedule("*/1 * * * *", updateCSVData);
// updateCSVData();
async function updateCSVData() {
  try {
    console.log("Running scheduled task for updating Output Data.");
    const currentDate = new Date();
    const HoursAgo = new Date(currentDate.getTime() -  100 * 24 * 60 * 60 * 1000);
    // console.log("One Hour Ago:", oneHourAgo);
    const key = `processing-csv:- ${process.env.SERVER_ID || "Master"}`;
    const processingJobs = await prisma.job.findMany({
      where: {
        AND: [{ status: key }, { createdAt: { gte: HoursAgo } }],
      },
      select: {
        id: true,
      },
    });
    // console.log("Processing Jobs:", processingJobs);
    if (processingJobs.length > 0) {
      console.log(
        "Previous job still running. Skipping this iteration for CSV Data Fetching."
      );
      return;
    }
    console.log("Running scheduled task for updating CSV Data.");
    const jobs = await prisma.job.findMany({
      where: {
        OR: [
          {
            AND: [{ status: key }, { createdAt: { gte: HoursAgo } }],
          },
          { status: "pending" },
        ],
      },
      orderBy: [
        { singleCompany: "desc" },
        { ppcAudit: "desc" },
        { createdAt: "asc" },
      ],
    });
    for (const job of jobs) {
      console.log("Processing Job CSV data for job id:", job.id);
      await prisma.job.update({
        where: {
          id: job.id,
        },
        data: {
          status: key,
        },
      });
      await processJob(job);
      clearCache();
      await closeBrowsers();
      await reloadServer();
    }
  } catch (error) {
    console.error("Error Stack:", error.stack);
    Sentry.captureException("Error in scheduled task for job:", error);
    console.error("An error occurred in scheduled task for job:", error);
  }
}

async function reprocessCompanyData(companyId, clientId) {
  try {
    console.log("Reprocessing company data for companyId:", companyId);
    const companyData = await prisma.company.findFirst({
      where: {
        id: companyId,
      }, select: {
        name: true,
        id:true,
      }
    });
    if (companyData) {
      await scrapeData(companyData, clientId);
    }
  } catch (error) {
    console.error("Error Stack:", error.stack);
    Sentry.captureException("Error in reprocessing company data:", error);
    console.error("An error occurred in reprocessing company data:", error);
  }
}

async function processJob(job, serverID) {
  try {
    console.log("Checking job status for jobId:", job.id);
    
    const key = `processing-csv:- ${process.env.SERVER_ID || "Main"}`;
    await prisma.job.update({
      where: {
        id: job.id
      },
      data: {
        status: key
      }
    })
    // Getting All the Company Data for the Job
    const outputData = await prisma.outputData.findMany({
      where: {
        jobId: job.id,
      },
    });

    // Flag to check the availability of data
    // [allDataAvailable]
    let dataFlag = [true];

    // Counter for the company data
    let counter = [1, outputData.length];
    console.log("Total Company Count:", counter[1]);

    for (const companyData of outputData) {
      await reprocessCompanyData(companyData.companyId, job.clientId);
      const cachedData = await getFromCache(
        `output-data-${companyData.companyId}-${job.clientId}`
      );
      if (cachedData) {
        counter[0]++;
        console.log("Using Cached Data for company: ", cachedData.companyName);
        companyData["aboutDataStatus"] = cachedData.aboutDataStatus;
        companyData["homepageDataStatus"] = cachedData.homepageDataStatus;
        companyData["amazonSearchUrl"] = cachedData.amazonSearchUrl;
        companyData["amazonDataStatus"] = cachedData.amazonDataStatus;
        companyData["qualificationStatus"] = cachedData.qualificationStatus;
        companyData["promptTokens"] = cachedData.promptTokens;
        companyData["completionTokens"] = cachedData.completionTokens;
        companyData["competitorDetails"] = cachedData.competitorDetails;
        companyData["prospectDetails"] = cachedData.prospectDetails;
        companyData["inputPrice"] = cachedData.inputPrice;
        companyData["outputPrice"] = cachedData.outputPrice;
        companyData["compKeyPrompt"] = cachedData.compKeyPrompt;
        companyData["searchKeyword"] = cachedData.searchKeyword;
        companyData["revenueDifference"] = cachedData.revenueDifference;
        companyData["amazonAudit"] = cachedData.amazonAudit;
        companyData["auditReport"] = cachedData.auditReport;
        companyData["auditMailData"] = cachedData.auditMailData;
        companyData["productSlug"] = cachedData.productSlug;
        companyData["companySlug"] = cachedData.companySlug;
        companyData["caseStudies"] = cachedData.caseStudies;
        companyData["category"] =
          typeof cachedData.category === "string"
            ? JSON.parse(cachedData.category)
            : cachedData.category ?? {};
        companyData["gptDetails"] = cachedData.gptDetails;
        companyData["userPrompt"] = cachedData.userPrompt;
        companyData["mailData"] = cachedData.mailData;
        companyData["pricing"] = cachedData.pricing;
        if (companyData["amazonDataStatus"] == "completed") {
          // If the amazon data is present then only generate the audit data && mail data
          await processFinalData(companyData, job.clientId, job.auditPdf);
          await processCompMail(companyData, job.clientId);
        }
      } else {
        await processCompanyData(
          companyData,
          dataFlag,
          counter,
          job.clientId,
          job.ppcAudit,
          job.singleCompany,
          job.auditPdf
        );
        await cache(
          `output-data-${companyData.companyId}-${job.clientId}`,
          companyData.id
        );
      }

      await prisma.outputData.update({
        where: {
          id: companyData.id,
        },
        data: companyData,
      });
      console.log(
        "Company csv data updated for -----------------",
        companyData.companyName
      );
    }
    // console.log("Data Flag:", dataFlag[0]);

    if (dataFlag[0] === true) {
      console.log("All Data Available for JobId:-----------------", job.id);
      await prisma.job.update({
        where: {
          id: job.id,
        },
        data: {
          status: "completed",
          qualifiedLeads: getQualifiedLeadsCount(outputData),
        },
      });
      console.log(
        "Job status updated to completed for jobId:----------------- ",
        job.id
      );
      sendErrorEmail("reportReadyAlert");
    } else {
      await prisma.job.update({
        where: {
          id: job.id,
        },
        data: {
          status: "pending",
        },
      });
    }
  } catch (error) {
    console.error("Error Stack:", error.stack);
    Sentry.captureException("Error in processing Job:", error);
    console.error("Error in processing Job:", error);
  }
}

async function processCompanyData(
  companyFinalData,
  dataFlag,
  counter,
  clientId,
  ppcAudit,
  singleCompany,
  auditPdf
) {
  try {
    console.log(
      "Checking Data for:-----------------",
      counter[0]++,
      " / ",
      counter[1],
      " -> Company Name: ",
      companyFinalData.companyName
    );

    // Placeholder for the GPT Prompt Data
    let userPrompt = {
      "Company Name": companyFinalData.companyName,
      "First Name": companyFinalData.firstName,
      "Amazon Data": {},
      "Web Data": {},
    };
    let csvData = companyFinalData;

    // const aboutData = await processAboutData(userPrompt, csvData, dataFlag);
    const amazonData = await processAmazonData(userPrompt, csvData, dataFlag);
    const aboutData = {
      status: "completed",
    };
    if (
      aboutData &&
      aboutData.status === "pending" &&
      amazonData &&
      amazonData.status === "pending"
    ) {
      dataFlag[0] = dataFlag[0] && false;
      return;
    }
    // If about data or amazon data exists
    if (
      (aboutData && aboutData.status === "completed") ||
      (amazonData && amazonData.status === "completed")
    ) {
      dataFlag[0] = dataFlag[0] && true;
      console.log(
        "Some Data exists for company:-----------------",
        csvData.companyName
      );
      // Update the qualificationStatus
      csvData["qualificationStatus"] = "qualified";
      //  TODO Check if the comparison is working

      // Generating Audit Data
      if (csvData["amazonDataStatus"] == "completed") {
        // If the amazon data is present then only generate the audit data && mail data
        if (!singleCompany || ppcAudit) {
          await processCompetitionData(csvData, amazonData, clientId);
        }
        await processAuditData(csvData, amazonData, clientId, auditPdf);
        await processFinalData(csvData, clientId, auditPdf);
        await processCompMail(csvData, clientId);
        console.log("PPC AUDIT FLAG:", ppcAudit);
        if (ppcAudit) {
          await processPPCAudit(csvData, amazonData, clientId, auditPdf);
        }
        csvData.pricing = JSON.parse(JSON.stringify(getTotalPrice()));
        await prisma.outputData.update({
          where: {
            id: csvData.id,
          },
          data: csvData,
        });
        resetPricing();
      }
    }

    console.log(
      "Updating CSV Data for company:-----------------",
      csvData.companyName
    );
  } catch (error) {
    console.error("Error Stack:", error.stack);
    Sentry.captureException("Error in processing Company Data:", error);
    console.error("Error in processing Company Data:", error);
  }
}

async function processCompMail(csvData, clientId) {
  try {
    console.log("Processing Mail Data");
    const { compEmailStatus, interpolatedString } = await generateCompEmail(
      csvData["finalData"],
      clientId
    );
    csvData["mailData"] = interpolatedString;
    csvData["finalData"]["Competitor Email"] = interpolatedString;
    csvData["finalData"]["Status"] = compEmailStatus;
    try {
        // Extract data needed for encoded audit slug
        const auditSlug = csvData["productSlug"] || '';
        const email = csvData["email"] || '';
        // Store the id of the company
        const sellerId = String(csvData.id);
        // Store the name of the client in the clientIdStr field
        const client = await prisma.user.findFirst({
          where: {
            id: clientId
          }
        })
        const clientIdStr = client.name || '';
        
        if (auditSlug) {
          // Debug: Log the values being passed to createEncodedURL
          console.log("Values being passed to createEncodedURL:", {
            productSlug: auditSlug,
            email,
            sellerId,
            clientId: clientIdStr
          });
          
          // Generate the encoded URL using createEncodedURL function
          const encodedUrlPath = createEncodedURL({
            productSlug: auditSlug,
            email,
            sellerId,
            clientId: clientIdStr
          });
          
          // Store the encoded audit slug in finalData
          csvData["finalData"]["encodedauditslug"] = encodedUrlPath;
          
          // Also generate complete URLs for reference
          const baseUrl = 'https://www.amazongrowthaudit.com';
          const encodedUrl = `${baseUrl}${encodedUrlPath}`;
          
          
          // Store URLs for reference (optional)
          csvData["finalData"]["encodedauditurl"] = encodedUrl;
          
          console.log("Encoded audit slug generated successfully:", {
            auditSlug,
            encodedSlug: encodedUrlPath,
            encodedUrl,
          });
        } else {
          console.log("No audit slug found, skipping encoded audit slug generation");
          // Set default empty values to prevent undefined errors
          csvData["finalData"]["encodedauditurl"] = '';
        }
      } catch (encodingError) {
        console.error("Error generating encoded audit slug:", encodingError);
        // Don't fail the entire process if encoding fails - set default values
        csvData["finalData"]["encodedauditslug"] = '';
        csvData["finalData"]["encodedauditurl"] = '';
      }
    
    console.log("Email Generated SuccessFully");
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.log("Error in Email", error);
  }
}
const getQualifiedLeadsCount = (outputData) => {
  return outputData.filter((data) => data.mailData && data.mailData.length > 0)
    .length;
};
module.exports = { updateCSVData , processJob};
