// Load environment variables first
require('dotenv').config();

const { EC2Client, DescribeInstancesCommand, StartInstancesCommand, StopInstancesCommand, RebootInstancesCommand, ModifyInstanceAttributeCommand } = require('@aws-sdk/client-ec2');
const { SSMClient, SendCommandCommand } = require('@aws-sdk/client-ssm');

// Check if AWS credentials are configured
const accessKeyId = process.env.AWS_ACCESS_KEY_ID;
const secretAccessKey = process.env.AWS_SECRET_ACCESS_KEY;
const region = process.env.AWS_REGION || 'ap-south-1'; // Default to ap-south-1 if not specified

if (!accessKeyId || !secretAccessKey) {
  console.warn('AWS credentials are not properly configured in environment variables.');
  console.warn('Please ensure AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY are set in the .env file.');
}

// Configure AWS SDK v3 with fallback values
const ec2Client = new EC2Client({
  region,
  credentials: {
    accessKeyId,
    secretAccessKey
  }
});

// Configure SSM client
const ssmClient = new SSMClient({
  region,
  credentials: {
    accessKeyId,
    secretAccessKey
  }
}); 

// Check if the user has EC2 permissions
async function validateEC2Permissions() {
  try {
    // Attempt a simple EC2 operation to check permissions
    const command = new DescribeInstancesCommand({ MaxResults: 5 });
    await ec2Client.send(command);
    return {
      hasPermission: true
    };
  } catch (error) {
    console.error('EC2 permission validation error:', error);

    // Determine the type of error
    let errorDetails = {
      hasPermission: false,
      message: error.message,
      code: error.Code || error.name,
      recommendation: ''
    };

    // Add specific recommendations based on error type
    if (error.message.includes('not authorized to perform')) {
      errorDetails.recommendation = 'The AWS user lacks EC2 permissions. Go to AWS IAM Console and add the "AmazonEC2FullAccess" policy to this user, or create a new user with EC2 permissions.';
    } else if (error.message.includes('credential') || error.message.includes('authentication')) {
      errorDetails.recommendation = 'The AWS credentials appear to be invalid. Please verify your AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY in the .env file.';
    } else if (error.message.includes('region')) {
      errorDetails.recommendation = 'There is an issue with the AWS region configuration. Make sure AWS_REGION is set correctly in the .env file.';
    } else {
      errorDetails.recommendation = 'An unknown error occurred. Please check your AWS configuration and network connectivity.';
    }

    return errorDetails;
  }
}

// Get instances
async function listInstances() {
  try {
    // Validate EC2 permissions before proceeding
    const permissionCheck = await validateEC2Permissions();
    if (!permissionCheck.hasPermission) {
      return Promise.reject(new Error(`AWS permission error: ${permissionCheck.recommendation}`));
    }

    const command = new DescribeInstancesCommand({});
    const data = await ec2Client.send(command);
    const instances = [];

    if (!data.Reservations) {
      return instances; // Return empty array if no instances found
    }

    const availableInstances = ["i-07de0db1de0fe4f74", "i-0eca89a83b7c830be", "i-0b9f2d0b5678231ec", "i-016c12c00360ca292"];
    
    data.Reservations.forEach(reservation => {
      reservation.Instances.forEach(instance => {
        if (!availableInstances.includes(instance.InstanceId)) {
          return;
        }
        // Find the Name tag
        const nameTag = instance.Tags?.find(tag => tag.Key === 'Name');

        instances.push({
          InstanceId: instance.InstanceId,
          InstanceType: instance.InstanceType,
          State: instance.State.Name,
          PublicIpAddress: instance.PublicIpAddress || 'N/A',
          PrivateIpAddress: instance.PrivateIpAddress || 'N/A',
          Name: nameTag ? nameTag.Value : 'Unnamed',
          LaunchTime: instance.LaunchTime
        });
      });
    });

    return instances;
  } catch (error) {
    console.error('Error listing instances:', error);
    // Enhance error messages for permission issues
    if (error.message.includes('not authorized')) {
      throw new Error('EC2 permission denied: Your AWS user does not have EC2 permissions. Please add the AmazonEC2FullAccess policy to this user in AWS IAM Console.');
    }
    throw new Error(`Failed to list EC2 instances: ${error.message}`);
  }
}


// Start an instance
async function startInstance(instanceId) {
  try {
    // Validate EC2 permissions before proceeding
    const permissionCheck = await validateEC2Permissions();
    if (!permissionCheck.hasPermission) {
      return {
        success: false,
        message: `Unable to start instance: ${permissionCheck.recommendation}`
      };
    }

    // Check instance current state
    const instances = await listInstances();
    const instance = instances.find(inst => inst.InstanceId === instanceId);

    if (!instance) {
      return {
        success: false,
        message: `Instance ${instanceId} not found`
      };
    }

    // If the instance is already running, return success
    if (instance.State === 'running') {
      return {
        success: true,
        message: `Instance ${instanceId} is already running`
      };
    }

    if (instance.State === 'stopped') {
      try {
        // Start the instance
        const command = new StartInstancesCommand({ InstanceIds: [instanceId] });
        await ec2Client.send(command);
      } catch (startError) {
        console.error('Start instance error:', startError);
        return {
          success: false,
          message: `Failed to start instance: ${startError.message}`
        };
      }
    }

    // Successful start, now wait for it to be running and deploy
    console.log(`Started instance ${instanceId}, waiting for it to be running...`);

    // // For deployment, we need to ensure the instance is fully running with a valid IP
    // let deploymentDetails = null;
    // try {
    //   // Check if instance is running every 5 seconds, up to 120 seconds (2 minutes)
    //   const maxAttempts = 24; // 2 minutes = 24 attempts of 5 seconds each
    //   for (let attempt = 0; attempt < maxAttempts; attempt++) {
    //     // Wait 5 seconds between checks
    //     await new Promise(resolve => setTimeout(resolve, 5000));

    //     // Get fresh instance data
    //     const updatedInstances = await listInstances();
    //     const updatedInstance = updatedInstances.find(inst => inst.InstanceId === instanceId);

    //     if (!updatedInstance) {
    //       console.log(`Warning: Could not find instance ${instanceId} after starting`);
    //       break;
    //     }

    //     console.log(`Instance ${instanceId} state: ${updatedInstance.State} (attempt ${attempt + 1}/${maxAttempts})`);

    //     if (updatedInstance.State === 'running') {
    //       // Make sure it has an IP
    //       if (updatedInstance.PublicIpAddress !== 'N/A' || updatedInstance.PrivateIpAddress !== 'N/A') {
    //         // Successfully running with an IP, try deploying
    //         console.log(`Instance ${instanceId} is running, attempting to deploy...`);

    //         // Wait an additional 30 seconds to make sure services are up
    //         await new Promise(resolve => setTimeout(resolve, 30000));

    //         try {
    //           // Run startup script via SSM
    //           const startupResult = await runStartupScript(instanceId);
    //           deploymentDetails = {
    //             success: startupResult.success,
    //             message: startupResult.message,
    //             commandId: startupResult.commandId
    //           };
    //         } catch (deployError) {
    //           console.error('Deployment error:', deployError);
    //         }

    //         break;
    //       }
    //     }

    //     // If the instance is terminating or stopped, break the loop
    //     if (['terminating', 'terminated', 'stopped', 'stopping'].includes(updatedInstance.State)) {
    //       console.log(`Instance ${instanceId} entered state ${updatedInstance.State}, stopping deployment attempts`);
    //       break;
    //     }
    //   }
    // } catch (waitError) {
    //   console.error('Error waiting for instance to be running:', waitError);
    // }

    return {
      success: true,
      message: `Instance ${instanceId} started successfully`,
    };
  } catch (error) {
    console.error('Error starting instance:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Stop an instance
async function stopInstance(instanceId) {
  try {
    // Validate EC2 permissions before proceeding
    const permissionCheck = await validateEC2Permissions();
    if (!permissionCheck.hasPermission) {
      return {
        success: false,
        message: `Unable to stop instance: ${permissionCheck.recommendation}`
      };
    }

    const command = new StopInstancesCommand({
      InstanceIds: [instanceId]
    });

    await ec2Client.send(command);

    return { success: true, message: `Instance ${instanceId} stopping...` };
  } catch (error) {
    console.error(`Error stopping instance ${instanceId}:`, error);
    // Enhance error messages for permission issues
    if (error.message.includes('not authorized')) {
      return {
        success: false,
        message: 'EC2 permission denied: Your AWS user does not have permission to stop EC2 instances. Please add the AmazonEC2FullAccess policy to this user in AWS IAM Console.'
      };
    }
    throw error;
  }
}

// Reboot an instance
async function rebootInstance(instanceId) {
  try {
    // Validate EC2 permissions before proceeding
    const permissionCheck = await validateEC2Permissions();
    if (!permissionCheck.hasPermission) {
      return {
        success: false,
        message: `Unable to reboot instance: ${permissionCheck.recommendation}`
      };
    }

    // Get the current instance state
    const instances = await listInstances();
    const instance = instances.find(inst => inst.InstanceId === instanceId);

    if (!instance) {
      return {
        success: false,
        message: `Instance ${instanceId} not found`
      };
    }

    // If the instance is not running, we can't reboot it
    if (instance.State !== 'running') {
      return {
        success: false,
        message: `Instance ${instanceId} is not running (current state: ${instance.State}). Cannot reboot.`
      };
    }

    // Normal reboot
    const command = new RebootInstancesCommand({
      InstanceIds: [instanceId]
    });

    await ec2Client.send(command);

    console.log(`Rebooted instance ${instanceId}, waiting for it to be running...`);

    // // For deployment, we need to ensure the instance is fully running
    // let deploymentDetails = null;
    // try {
    //   // Give the instance some time to start the reboot process
    //   await new Promise(resolve => setTimeout(resolve, 30000)); // 30 seconds

    //   // Check if instance is running every 5 seconds, up to 120 seconds (2 minutes)
    //   const maxAttempts = 24; // 2 minutes = 24 attempts of 5 seconds each
    //   let instanceRunning = false;

    //   for (let attempt = 0; attempt < maxAttempts; attempt++) {
    //     // Wait 5 seconds between checks
    //     await new Promise(resolve => setTimeout(resolve, 5000));

    //     // Get fresh instance data
    //     const updatedInstances = await listInstances();
    //     const updatedInstance = updatedInstances.find(inst => inst.InstanceId === instanceId);

    //     if (!updatedInstance) {
    //       console.log(`Warning: Could not find instance ${instanceId} after rebooting`);
    //       break;
    //     }

    //     console.log(`Instance ${instanceId} state: ${updatedInstance.State} (attempt ${attempt + 1}/${maxAttempts})`);

    //     if (updatedInstance.State === 'running') {
    //       // Successfully running, wait a bit for services to start and try deploying
    //       console.log(`Instance ${instanceId} is running after reboot, waiting for services...`);

    //       // Wait an additional 60 seconds after reboot to make sure all services are up
    //       await new Promise(resolve => setTimeout(resolve, 60000));

    //       try {
    //         // Run startup script via SSM
    //         const startupResult = await runStartupScript(instanceId);
    //         deploymentDetails = {
    //           success: startupResult.success,
    //           message: startupResult.message,
    //           commandId: startupResult.commandId
    //         };
    //       } catch (deployError) {
    //         console.error('Deployment error after reboot:', deployError);
    //       }

    //       instanceRunning = true;
    //       break;
    //     }
    //   }

    //   if (!instanceRunning) {
    //     return {
    //       success: false,
    //       message: `Instance ${instanceId} did not enter running state after reboot`
    //     };
    //   }
    // } catch (waitError) {
    //   console.error('Error waiting for instance to be running after reboot:', waitError);
    // }

    return {
      success: true,
      message: `Instance ${instanceId} rebooted successfully`,
    };
  } catch (error) {
    console.error('Error rebooting instance:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Run startup script commands via SSM on the specified EC2 instance
 * @param {string} instanceId - The EC2 instance ID
 * @returns {Promise<Object>} Result of the operation
 */
async function runStartupScript(instanceId) {
  if (!instanceId) {
    throw new Error("Instance ID is required");
  }

  try {
    const timestamp = new Date().toISOString().replace(/:/g, '-');
    
    console.log(`[${timestamp}] Running startup script via SSM on instance ${instanceId}`);

    const params = {
      DocumentName: "AWS-RunShellScript",
      InstanceIds: [instanceId],
      Parameters: {
        commands: [
          "#!/bin/bash",
          "set -x", // Enable debugging

          // Create logs directory in jeff-core
          "cd /home/<USER>/jeff-core",
          "mkdir -p logs",

          // Setup logging with timestamp
          "TIMESTAMP=$(date +\"%Y-%m-%d_%H-%M-%S\")",
          "LOG_FILE=\"logs/startup_${TIMESTAMP}.log\"",
          "ERROR_FILE=\"logs/error_${TIMESTAMP}.log\"",
          "echo \"Starting jeff-core setup $(date)\" >> $LOG_FILE",

          // Update repository
          "git checkout main",
          "git pull >> $LOG_FILE 2>> $ERROR_FILE",

          // Install dependencies
          "npm install >> $LOG_FILE 2>> $ERROR_FILE",

          // Restart application with PM2
          "pm2 delete all >> $LOG_FILE 2>> $ERROR_FILE || true",
          "pm2 start npm --name \"jeff\" -- start --node-args=\"--max-old-space-size=4048\" >> $LOG_FILE 2>> $ERROR_FILE",

          // Setup PM2 startup
          "pm2 save >> $LOG_FILE 2>> $ERROR_FILE",
          "echo \"Setup completed $(date)\" >> $LOG_FILE"
        ],
      },
    };

    const command = new SendCommandCommand(params);
    const result = await ssmClient.send(command);

    console.log(`[${timestamp}] SSM command executed successfully. Command ID: ${result.Command.CommandId}`);

    return {
      success: true,
      message: `Startup script executed successfully on instance ${instanceId}`,
      commandId: result.Command.CommandId
    };
  } catch (error) {
    console.error("Failed to execute startup script:", error);
    return {
      success: false,
      message: `Failed to execute startup script on instance ${instanceId}`,
      error: error.message
    };
  }
}

// Test function if the file is run directly
if (require.main === module) {
  console.log('Testing AWS Instance Manager...');
  console.log('AWS Region:', region);
  console.log('AWS Credentials configured:', !!accessKeyId && !!secretAccessKey);

  const testInstanceId = 'i-07de0db1de0fe4f74';

  // List instances
  // listInstances().then(instances => {
  //   console.log(`Found ${instances.length} instances:`);
  //   instances.forEach(inst => {
  //     console.log(`${inst.InstanceId} (${inst.State})`);
  //   });
  // }).catch(console.error);

  // For manual testing uncomment one of these:

  // Test starting an instance
  // startInstance(testInstanceId).then(result => {
  //   console.log('Start instance result:', result);
  // }).catch(console.error);

  // Test stopping an instance
  // stopInstance(testInstanceId).then(result => {
  //   console.log('Stop instance result:', result);
  // }).catch(console.error);

  // Test rebooting an instance
  // rebootInstance(testInstanceId).then(result => {
  //   console.log('Reboot instance result:', result);
  // }).catch(console.error);

  // Test running startup script
  runStartupScript(testInstanceId).then(result => {
    console.log('Startup script result:', result);
  }).catch(console.error);

}

module.exports = {
  validateEC2Permissions,
  listInstances,
  startInstance,
  stopInstance,
  rebootInstance,
  runStartupScript
}; 