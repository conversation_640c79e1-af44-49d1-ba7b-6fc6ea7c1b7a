const azureInstanceManager = require('../services/azure/azureInstanceManager');

// List all VM instances
const listInstances = async (req, res) => {
  try {
    const instances = await azureInstanceManager.listInstances();
    return res.status(200).json({ 
      success: true, 
      data: instances 
    });
  } catch (error) {
    console.error('Error in listInstances controller:', error);
    
    // Check for specific error types
    if (error.message.includes('credentials')) {
      return res.status(503).json({ 
        success: false, 
        error: 'Azure credentials are not configured properly. Please contact the administrator.' 
      });
    }
    
    if (error.message.includes('subscription')) {
      return res.status(503).json({ 
        success: false, 
        error: 'Azure subscription is not configured properly. Please contact the administrator.' 
      });
    }
    
    if (error.message.includes('authorization')) {
      return res.status(403).json({ 
        success: false, 
        error: 'Insufficient permissions to access Azure resources. Please contact the administrator.' 
      });
    }
    
    return res.status(500).json({ 
      success: false, 
      error: error.message || 'Failed to fetch Azure VM instances' 
    });
  }
};

// Start a VM instance
const startInstance = async (req, res) => {
  try {
    // Use either instanceId or id parameter (for compatibility with existing routes)
    const vmName = req.params.instanceId || req.params.id;
    
    if (!vmName) {
      return res.status(400).json({ 
        success: false, 
        error: 'VM name is required' 
      });
    }
    
    const result = await azureInstanceManager.startInstance(vmName);
    return res.status(200).json({ 
      success: result.success, 
      message: result.message,
      deploymentDetails: result.deploymentDetails
    });
  } catch (error) {
    console.error('Error in startInstance controller:', error);
    return res.status(500).json({ 
      success: false, 
      error: error.message || 'Failed to start VM instance' 
    });
  }
};

// Stop a VM instance
const stopInstance = async (req, res) => {
  try {
    // Use either instanceId or id parameter (for compatibility with existing routes)
    const vmName = req.params.instanceId || req.params.id;
    
    if (!vmName) {
      return res.status(400).json({ 
        success: false, 
        error: 'VM name is required' 
      });
    }
    
    const result = await azureInstanceManager.stopInstance(vmName);
    return res.status(200).json({ 
      success: result.success, 
      message: result.message 
    });
  } catch (error) {
    console.error('Error in stopInstance controller:', error);
    return res.status(500).json({ 
      success: false, 
      error: error.message || 'Failed to stop VM instance' 
    });
  }
};

// Reboot a VM instance
const rebootInstance = async (req, res) => {
  try {
    // Use either instanceId or id parameter (for compatibility with existing routes)
    const vmName = req.params.instanceId || req.params.id;
    
    if (!vmName) {
      return res.status(400).json({ 
        success: false, 
        error: 'VM name is required' 
      });
    }
    
    const result = await azureInstanceManager.rebootInstance(vmName);
    return res.status(200).json({ 
      success: result.success, 
      message: result.message,
      deploymentDetails: result.deploymentDetails
    });
  } catch (error) {
    console.error('Error in rebootInstance controller:', error);
    return res.status(500).json({ 
      success: false, 
      error: error.message || 'Failed to reboot VM instance' 
    });
  }
};

// Run startup script on a VM instance
const runStartupScript = async (req, res) => {
  try {
    // Use either instanceId or id parameter (for compatibility with existing routes)
    const vmName = req.params.instanceId || req.params.id;
    
    if (!vmName) {
      return res.status(400).json({ 
        success: false, 
        error: 'VM name is required' 
      });
    }
    
    const result = await azureInstanceManager.runStartupScript(vmName);
    return res.status(200).json({ 
      success: result.success, 
      message: result.message,
      note: result.note
    });
  } catch (error) {
    console.error('Error in runStartupScript controller:', error);
    return res.status(500).json({ 
      success: false, 
      error: error.message || 'Failed to run startup script on VM instance' 
    });
  }
};

module.exports = {
  listInstances,
  startInstance,
  stopInstance,
  rebootInstance,
  runStartupScript
};
