-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "ReviewStatus" ADD VALUE 'VIOLATION_DETECTION_PENDING';
ALTER TYPE "ReviewStatus" ADD VALUE 'VIOLATION_DETECTION_IN_PROGRESS';
ALTER TYPE "ReviewStatus" ADD VALUE 'VIOLATION_DETECTED';
ALTER TYPE "ReviewStatus" ADD VALUE 'NO_VIOLATION_DETECTED';

-- AlterTable
ALTER TABLE "LexPromptChain" ADD COLUMN     "model" TEXT NOT NULL DEFAULT 'azure-gpt4o';

-- AlterTable
ALTER TABLE "LexReview" ADD COLUMN     "autoPromptEnabled" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "isClient" BOOLEAN DEFAULT false,
ADD COLUMN     "manualPromptRequested" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "metadata" JSONB DEFAULT '{}',
ADD COLUMN     "promptExecutionJobId" INTEGER,
ADD COLUMN     "violationConfidence" DOUBLE PRECISION,
ADD COLUMN     "violationReason" TEXT;

-- CreateIndex
CREATE INDEX "LexPromptChain_model_idx" ON "LexPromptChain"("model");

-- CreateIndex
CREATE INDEX "LexReview_isClient_idx" ON "LexReview"("isClient");

-- CreateIndex
CREATE INDEX "LexReview_autoPromptEnabled_idx" ON "LexReview"("autoPromptEnabled");
