// Configuration file for AmazonProductData to Product migration

// Load environment variables from .env file
require("dotenv").config();

// Target database configuration
let targetDbConfig = {
    host: 'localhost',
    port: 5432,
    database: 'target_database',
    username: 'username',
    password: 'password',
    ssl: false,
};

// Parse TARGET_DATABASE_URL from .env if provided
if (process.env.TARGET_DATABASE_URL) {
    const url = new URL(process.env.TARGET_DATABASE_URL);
    console.log({ url });
    targetDbConfig = {
        host: url.hostname,
        port: parseInt(url.port),
        database: url.pathname.substring(1), // Remove leading slash
        username: decodeURIComponent(url.username),
        password: decodeURIComponent(url.password),
        ssl: {
            rejectUnauthorized: false,
            require: true,
            ca: undefined
        },
        // Additional Azure PostgreSQL settings
        connectionTimeoutMillis: 30000,
        idleTimeoutMillis: 30000,
        max: 20, // Maximum number of clients in the pool
    };

    console.log(`🔗 Connecting to target database: ${url.hostname}:${url.port}/${url.pathname.substring(1)}`);
} else {
    // Fallback to individual environment variables if TARGET_DATABASE_URL is not provided
    targetDbConfig = {
        host: process.env.TARGET_DB_HOST || 'localhost',
        port: parseInt(process.env.TARGET_DB_PORT) || 5432,
        database: process.env.TARGET_DB_NAME || 'target_database',
        username: process.env.TARGET_DB_USER || 'username',
        password: process.env.TARGET_DB_PASSWORD || 'password',
        ssl: process.env.TARGET_DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
    };
}

// Migration configuration
const migrationConfig = {
    batchSize: parseInt(process.env.MIGRATION_BATCH_SIZE) || 50,
    maxRetries: parseInt(process.env.MIGRATION_MAX_RETRIES) || 3,
    retryDelay: parseInt(process.env.MIGRATION_RETRY_DELAY) || 1000, // milliseconds
    skipExisting: process.env.MIGRATION_SKIP_EXISTING === 'true' || true,
    updateExisting: process.env.MIGRATION_UPDATE_EXISTING === 'true' || false,
};

// Field mapping configuration
const fieldMapping = {
    // Basic product information
    url: null, // Will be handled specially to use "N/A" when null
    brand_name: 'data.company_name',
    product_title: 'data.productData[0].productTitle.value',
    description: 'data.productData[0].textDescription.value',
    price: 'data.productData[0].price',

    // Ratings & reviews
    rating: 'data.productData[0].rating.rating',
    total_reviews: 'data.productData[0].review.totalReviewCountInt',
    review_category: 'data.productData[0].review.reviewsCategory',
    star_5_count: null, // Will be extracted from review data if available
    star_4_count: null,
    star_3_count: null,
    star_2_count: null,
    star_1_count: null,
    sales_count: 'data.productData[0].sales',

    // Media
    main_image_url: null, // Will be extracted from productData if available
    image_count: 'data.productData[0].images.noOfImages',
    video_count: 'data.productData[0].images.noOfVideos',

    // Metadata
    title_char_count: 'data.productData[0].productTitle.numOfChars',
    title_under_150_chars: 'data.productData[0].productTitle.titleUnder150Chars',
    out_of_stock: false, // Default value

    // Amazon features
    aplus_content_present: 'data.productData[0].AplusContent.aplusContentPresent',
    premium_aplus_present: false, // Default value
    brand_story_present: false, // Default value
    storefront_present: 'data.store.storefront_present',
    storefront_url: 'data.store.store_front_url',

    // JSON fields
    bullet_points: 'data.productData[0].bulletPoints.Points',
    categories_and_ranks: 'data.productData[0].categoryAndRank',
    secondary_images: null, // Will be extracted from productData if available
    brand_story_images: null, // Will be extracted if available

    // System fields
    full_json_data: 'data', // Store the entire data object
    createdAt: 'updatedAt', // Use updatedAt from source as createdAt in target
    updatedAt: null, // Let database set updatedAt automatically
};

// Validation rules for the migration
const validationRules = {
    requiredFields: ['brand_name', 'product_title'],
    urlPattern: /^https?:\/\/.+/,
    maxTitleLength: 500,
    maxDescriptionLength: 10000,
    minPrice: 0,
    maxPrice: 999999.99,
    minRating: 0,
    maxRating: 5,
};

// Helper function to get nested object value
function getNestedValue(obj, path) {
    if (!path) return null;

    return path.split('.').reduce((current, key) => {
        if (current === null || current === undefined) return null;

        // Handle array access
        if (key.includes('[') && key.includes(']')) {
            const arrayKey = key.split('[')[0];
            const index = parseInt(key.match(/\[(\d+)\]/)[1]);
            return current[arrayKey] && current[arrayKey][index];
        }

        return current[key];
    }, obj);
}

// Helper function to validate data before migration
function validateProductData(productData) {
    const errors = [];

    // Check required fields
    for (const field of validationRules.requiredFields) {
        if (!productData[field]) {
            errors.push(`Missing required field: ${field}`);
        }
    }

    // Validate URL format (skip validation for "N/A" values)
    if (productData.url && productData.url !== "N/A" && !validationRules.urlPattern.test(productData.url)) {
        errors.push('Invalid URL format');
    }

    // Validate title length
    if (productData.product_title && productData.product_title.length > validationRules.maxTitleLength) {
        errors.push(`Product title too long (max ${validationRules.maxTitleLength} characters)`);
    }

    // Validate description length
    if (productData.description && productData.description.length > validationRules.maxDescriptionLength) {
        errors.push(`Description too long (max ${validationRules.maxDescriptionLength} characters)`);
    }

    // Validate price range
    if (productData.price !== null && productData.price !== undefined) {
        if (productData.price < validationRules.minPrice || productData.price > validationRules.maxPrice) {
            errors.push(`Price out of range (${validationRules.minPrice} - ${validationRules.maxPrice})`);
        }
    }

    // Validate rating range
    if (productData.rating !== null && productData.rating !== undefined) {
        if (productData.rating < validationRules.minRating || productData.rating > validationRules.maxRating) {
            errors.push(`Rating out of range (${validationRules.minRating} - ${validationRules.maxRating})`);
        }
    }

    return errors;
}

// Helper function to sanitize data
function sanitizeProductData(productData) {
    const sanitized = { ...productData };

    // Trim string fields
    const stringFields = ['url', 'brand_name', 'product_title', 'description', 'storefront_url'];
    stringFields.forEach(field => {
        if (sanitized[field] && typeof sanitized[field] === 'string') {
            sanitized[field] = sanitized[field].trim();
        }
    });

    // Ensure numeric fields are numbers
    const numericFields = ['price', 'rating', 'total_reviews', 'sales_count', 'image_count', 'video_count', 'title_char_count'];
    numericFields.forEach(field => {
        if (sanitized[field] !== null && sanitized[field] !== undefined) {
            const num = parseFloat(sanitized[field]);
            sanitized[field] = isNaN(num) ? null : num;
        }
    });

    // Ensure boolean fields are booleans
    const booleanFields = ['title_under_150_chars', 'out_of_stock', 'aplus_content_present', 'premium_aplus_present', 'brand_story_present', 'storefront_present'];
    booleanFields.forEach(field => {
        if (sanitized[field] === null || sanitized[field] === undefined) {
            sanitized[field] = false; // Default to false for null/undefined boolean fields
        } else {
            sanitized[field] = Boolean(sanitized[field]);
        }
    });

    return sanitized;
}

// Helper function to create database connection string
function getConnectionString() {
    const { host, port, database, username, password } = targetDbConfig;
    return `postgresql://${username}:${password}@${host}:${port}/${database}`;
}

// Helper function to test database connection
async function testConnection() {
    const { Client } = require('pg');
    const client = new Client({
        host: targetDbConfig.host,
        port: targetDbConfig.port,
        database: targetDbConfig.database,
        user: targetDbConfig.username,
        password: targetDbConfig.password,
        ssl: targetDbConfig.ssl,
        connectionTimeoutMillis: 5000,
    });

    try {
        await client.connect();
        const result = await client.query('SELECT NOW()');
        console.log('Database connection successful:', result.rows[0]);
        return true;
    } catch (error) {
        console.error('Database connection failed:', error.message);
        return false;
    } finally {
        await client.end();
    }
}

// Helper function to check if target table exists
async function checkTargetTable() {
    const { Client } = require('pg');
    const client = new Client({
        host: targetDbConfig.host,
        port: targetDbConfig.port,
        database: targetDbConfig.database,
        user: targetDbConfig.username,
        password: targetDbConfig.password,
        ssl: targetDbConfig.ssl,
        connectionTimeoutMillis: 10000,
    });

    try {
        await client.connect();
        const result = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'products'
      );
    `);

        const tableExists = result.rows[0].exists;
        console.log('Target table "products" exists:', tableExists);
        return tableExists;
    } catch (error) {
        console.error('Error checking target table:', error.message);
        return false;
    } finally {
        await client.end();
    }
}

module.exports = {
    targetDbConfig,
    migrationConfig,
    fieldMapping,
    validationRules,
    getNestedValue,
    validateProductData,
    sanitizeProductData,
    getConnectionString,
    testConnection,
    checkTargetTable,
}; 