const prisma = require("../../database/prisma/getPrismaClient");
const converter = require("json-2-csv"); // Ensure you have this package installed
const {parse} = require("csv-parse/sync")
const processCsvFiles = async (inputArray) => {
  try {
    const SLUG_TYPES = [
      "success-with-revenue",
      "success-without-revenue",
      "unsuccessful",
    ];
    // Fetch CSV data
    const fetchPromises = inputArray.flatMap((id) =>
      SLUG_TYPES.map((slug) =>
        fetchCsvData(id, slug).then((csv) => ({
          id,
          slug,
          csv,
        }))
      )
    );

    const responses = await Promise.all(fetchPromises);
    // console.log("RES AT 23:", responses);
    let successData = [];
    let unsuccessfulData = [];
    let successHeaders = [];
    let unsuccessfulHeaders = [];

    // Process CSV data
    responses.forEach(({ slug, csv }) => {
      if (!csv) return;

      try {
        const records = parse(csv, { columns: true, skip_empty_lines: true });
        if (!records.length) return;

        if (slug === "unsuccessful") {
          if (!unsuccessfulHeaders.length)
            unsuccessfulHeaders = Object.keys(records[0]);
          unsuccessfulData.push(...records);
        } else {
          if (!successHeaders.length) successHeaders = Object.keys(records[0]);
          successData.push(...records);
        }
      } catch (parseError) {
        console.error(`Error parsing CSV for slug: ${slug}`, parseError);
      }
    });

    if (!successData.length && !unsuccessfulData.length) {
      throw new Error("No valid CSV data found");
    }

    return {
      successData,
      unsuccessfulData,
      successHeaders,
      unsuccessfulHeaders,
    };
  } catch (error) {
    console.log(`An error occurred in processCsvFiles: ${error.message}`);
    return JSON.stringify({
      error: "Internal server error in processCsvFiles",
    });
  }
};

const fetchCsvData = async (id, slug) => {
  try {
    function formatRevenue(revenue) {
      if (revenue >= 1000000) {
        return (revenue / 1000000).toFixed() + "M";
      } else if (revenue >= 1000) {
        return (revenue / 1000).toFixed() + "K";
      } else {
        return Math.round(revenue).toString() || revenue;
      }
    }

    let outputData = null;
    const jobId = parseInt(id);
    console.log("Downloading CSV for Job ID:", jobId);
    console.log("Downloading CSV for Slug:", slug);

    // Find the Job with the given ID
    const job = await prisma.job.findUnique({ where: { id: jobId } });
    if (!job) {
      return res.status(404).json({ error: "Job not found" });
    }

    if (slug === "success-with-revenue") {
      // Fetch all data from the OutputData table where jobId matches and revenue > 0
      outputData = await prisma.outputData.findMany({
        where: {
          jobId,
          prospectDetails: {
            path: ["revenue"],
            gt: 0,
          },
          mailData: {
            not: "",
          },
        },
      });
    } else if (slug === "success-without-revenue") {
      // Fetch all data from the OutputData table where jobId matches and revenue = 0
      outputData = await prisma.outputData.findMany({
        where: {
          jobId,
          prospectDetails: {
            path: ["revenue"],
            lte: 0,
          },
          mailData: {
            not: "",
          },
        },
      });
    } else if (slug === "unsuccessful") {
      // Fetch all data from the OutputData table where jobId matches and qualificationStatus is "unqualified"
      outputData = await prisma.outputData.findMany({
        where: {
          jobId,
          mailData: "",
        },
      });
    } else {
      // Fetch all data from the OutputData table where jobId matches
      outputData = await prisma.outputData.findMany({
        where: {
          jobId,
        },
      });
    }
    // Transform the data for CSV conversion
    outputData = outputData.map((obj) => ({
      "Seller name": obj.companyName || "",
      "Seller name (humanized)":
        obj.prospectDetails.humanizedProspectName || "",
      "Seller Website": obj.website || "",
      "Prospect LinkedIn URL": obj.sellerDetails["Prospect LinkedIn URL"] || "",
      "Storefront URL": obj.sellerDetails["Seller Storefront Link"] || "",
      "Prospect First Name": obj.sellerDetails["First Name"] || "",
      "Prospect Second Name": obj.sellerDetails["Second Name"] || "",
      "Job Title": obj.position || "",
      Email: obj.email || "",
      "Competitor Email": obj.mailData || "",
      Status: slug,
      "BSR Category":
        Array.isArray(obj.category) && obj.category.length > 0
          ? obj.category.find(
              (c) =>
                c.category &&
                typeof c.category === "string" &&
                c.category.trim()
            )?.category || ""
          : typeof obj.category === "string"
          ? obj.category
          : JSON.stringify(obj.category || ""),
      "Prospect product URL": obj.prospectDetails.productAmazonURL || "",
      "Prospect ASIN": obj.prospectDetails.asin || "",
      "Competitor Product URL": obj.competitorDetails.productAmazonURL || "",
      "Competitor ASIN": obj.competitorDetails.asin || "",
      "Audit webpage link": obj.finalData["Audit webpage link"] || "",
      "Audit PDF link": job.auditPdf ? (obj.finalData["Audit PDF link"] || "") : "N/A",
      "Amazon search URL": obj.amazonSearchUrl || "",
      "Prospect Product Title (Humanized)":
        obj.prospectDetails.humanizedProspectProductTitle || "",
      "Competitor Product Title (Humanized)":
        obj.competitorDetails.humanizedCompProductTitle || "",
      "Revenue difference monthly": formatRevenue(obj.revenueDifference) || 0,
      "Revenue difference yearly":
        formatRevenue(obj.revenueDifference * 12) || 0,
      "Competitor brand name (humanized)":
        obj.competitorDetails.humanizedCompCompanyName || "",
      "Prospect revenue": formatRevenue(obj.prospectDetails?.revenue || 0) || 0,
      "Competitor revenue":
        formatRevenue(obj.competitorDetails?.revenue || 0) || 0,
      "Annual Competitor Revenue":
        formatRevenue(obj.competitorDetails?.revenue * 12 || 0) || 0,
      "Video: calculated value":
        formatRevenue(obj.prospectDetails?.revenue * 0.097 || 0) || 0,
      "Revenue Source": obj.prospectDetails.revenueSource || "",
      "Number of optimization": Object.keys(obj.auditReport).length || 0,
      "Revenue source": obj.prospectDetails.revenueSource || "",
      "User Prompt": JSON.stringify(obj.userPrompt),
      "Prospect Details": JSON.stringify(obj.prospectDetails),
      "Competitor Details": JSON.stringify(obj.competitorDetails),
      "Amazon Audit": JSON.stringify(obj.amazonAudit),
      "Audit Report": JSON.stringify(obj.auditReport),
      "1 Star Reviews": obj.finalData["1 Star Reviews"],
      "2 Star Reviews": obj.finalData["2 Star Reviews"],
      "1 and 2 Star Reviews": obj.finalData["1 and 2 Star Reviews"],
      "Total Number Of Ratings": obj.finalData["Total Number Of Ratings"],
      "Number Of Stars": obj.finalData["Number Of Stars"],
      "Number Of Stars Comes As": obj.finalData["Number Of Stars Comes As"],
      "Goal for Number of stars": obj.finalData["Goal for Number of stars"],
      "Goal for Number of stars 'comes as' ":
        obj.finalData["Goal for Number of stars 'comes as' "],
      "Number of 5* ratings needed":
        obj.finalData["Number of 5* ratings needed"],
      "Minimum number of 1* & 2* to be removed": JSON.stringify(
        obj.finalData["Minimum number of 1* & 2* to be removed"]
      ),
      "Branded Keyword": obj.auditMailData?.brandedKeyword || "",
      "Non Branded Keyword": obj.auditMailData?.nonBrandedKeyword || "",
      "Main Image Optimisation":
        obj.auditMailData?.mainImageOptimisationText || "",
      "Data Point 1": obj.auditMailData?.Data_Point_1 || "",
      "Pain Point 1": obj.auditMailData?.Pain_Point_1 || "",
      "Top Improvement 1": obj.auditMailData?.Top_Improvement_1 || "",
      "Data Point 2": obj.auditMailData?.Data_Point_2 || "",
      "Pain Point 2": obj.auditMailData?.Pain_Point_2 || "",
      "Top Improvement 2": obj.auditMailData?.Top_Improvement_2 || "",
      "Data Point 3": obj.auditMailData?.Data_Point_3 || "",
      "Pain Point 3": obj.auditMailData?.Pain_Point_3 || "",
      "Top Improvement 3": obj.auditMailData?.Top_Improvement_3 || "",
      "Page Image": obj.finalData["Page Image"] || "",
      "Product Image": obj.finalData["Product Image"] || "",
      "Audit Mail Image": obj.finalData["Audit Mail Image"] || "",
      "Case Studies": JSON.stringify(obj.caseStudies),
      ID: obj.id,
      "Prompt Tokens": obj.promptTokens || 0,
      "Completion Tokens": obj.completionTokens || 0,
      "Homepage Data Status": obj.homepageDataStatus || "not-found",
      "Prompt Template": obj.promptTemplate || "",
      "GPT Details": obj.gptDetails || "",
      "Input Price": obj.inputPrice || 0.0,
      "Output Price": obj.outputPrice || 0.0,
      "Comp Key Prompt": obj.compKeyPrompt || "",
      "Search Keyword": obj.searchKeyword || "",
      "Company Slug": obj.companySlug || "",
      "Product Slug": obj.productSlug || "",
      "Job ID": obj.jobId,
      "Qualification Status": obj.qualificationStatus || "",
      "Amazon Status": obj.amazonDataStatus || "",
      "PPC Report": JSON.stringify(obj.ppcAudit),
      "Scraper API Credits Used": obj.pricing?.scraperApi?.creditUsed || 0,
      "Scraping Bee Credits Used": obj.pricing?.scrapingBee?.creditUsed || 0,
      "Jungle Scout Credits Used": obj.pricing?.jungleScout?.creditUsed || 0,
      "Chat GPT Input Credits Used": obj.pricing?.chatGPT?.inputToken || 0,
      "Chat GPT Output Credits Used": obj.pricing?.chatGPT?.outputToken || 0,
      "Scraper API Per Credit Cost (150)": 0.00015,
      "Scraping Bee Credit Cost (150)": 0.0001,
      "Jungle Scout Credit Cost (178)": 0.0445,
      "Chat GPT Input Per Token Cost": 0.0000025,
      "Chat GPT Output Per Token Cost": 0.00001,
      "Scraper API Total Cost": obj.pricing?.scraperApi?.creditUsed * 0.00015,
      "Scraping Bee Total Cost": obj.pricing?.scrapingBee?.creditUsed * 0.0001,
      "Jungle Scout Total Cost": obj.pricing?.jungleScout?.creditUsed * 0.0445,
      "Chat GPT Total Cost":
        obj.pricing?.chatGPT?.inputToken * 0.0000025 +
        obj.pricing?.chatGPT?.outputToken * 0.00001,
      "Total Cost":
        obj.pricing?.scraperApi?.creditUsed * 0.00015 +
        obj.pricing?.scrapingBee?.creditUsed * 0.0001 +
        obj.pricing?.jungleScout?.creditUsed * 0.0445 +
        (obj.pricing?.chatGPT?.inputToken * 0.0000025 +
          obj.pricing?.chatGPT?.outputToken * 0.00001),
      "Encoded Audit URL": obj.finalData["encodedauditurl"] || "",
      "Encoded Audit Slug": obj.finalData["encodedauditslug"] || "",
      "Company ID": obj.companyId,
      "Created At": obj.createdAt,
      "Updated At": obj.updatedAt,
    }));
    const csvContent = converter.json2csv(outputData);
    return csvContent;
  } catch (error) {
    console.error("Error fetching output data:", error);
    return new Error("Error fetching output data");
  }
};

const fetchCSVFileData = async (inputArray, table, indentifierKey, customFormatter = null) => {
  try {
    // Convert string IDs to integers
    const jobIds = inputArray.map(id => parseInt(id, 10));

    const data = await prisma[table].findMany({
      where: {
        [indentifierKey]: {
          in: jobIds
        }
      }
    });

    if (!data || data.length === 0) {
      console.log("No data found for the given job IDs");
      return new Error("No data found");
    }

    // Format data for Google Sheets API
    const formatDataForSheets = async (data) => {
      // If custom formatter is provided, use it
      if (customFormatter) {
        const formattedData = await customFormatter(data);
        if (formattedData instanceof Error) {
          return formattedData;
        }
        if (!Array.isArray(formattedData) || formattedData.length === 0) {
          return new Error("Invalid data format from custom formatter");
        }
        const headers = Object.keys(formattedData[0] || {});
        return [headers, ...formattedData.map(row => Object.values(row))];
      }

      // Default formatting
      const allKeys = [...new Set(data.flatMap(obj => Object.keys(obj)))];

      const formattedRows = data.map(item => {
        return allKeys.map(key => {
          const value = item[key];
          if (Array.isArray(value)) {
            return value.join(', ');
          } else if (value === null || value === undefined) {
            return '';
          } else if (typeof value === 'object') {
            return JSON.stringify(value);
          } else {
            return value;
          }
        });
      });

      return [allKeys, ...formattedRows];
    };

    const sheetData = await formatDataForSheets(data);
    if (sheetData instanceof Error) {
      return sheetData;
    }
    return sheetData;
  } catch (error) {
    console.error("Error in [fetchCSVFileData] fetching CSV data:", error);
    return new Error("Error fetching CSV data");
  }
};

module.exports = { processCsvFiles, fetchCSVFileData };