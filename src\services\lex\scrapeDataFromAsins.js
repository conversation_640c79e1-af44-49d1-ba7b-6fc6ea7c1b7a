const cheerio = require("cheerio");
const fs = require("fs");
const path = require("path");
const { getHtmlByProxy } = require("../../utils/getHtmlByProxy");
const getCategoryAndRank = require("../../utils/scrapeAmazonUtils/getCategoryAndRank");
const { getAmazonTLD } = require("../../utils/lexUtils/countryCodeMapping");

// Function to extract seller info from HTML
function getSellerInfo($) {
  const container = $('[data-csa-c-slot-id="odf-feature-text-desktop-merchant-info"] .a-spacing-none.odf-truncation-popover');
  if (!container.length) return { sellerId: null, sellerName: null };

  const anchor = container.find('a').first();
  if (!anchor.length) return { sellerId: null, sellerName: null };

  const href = anchor.attr('href');
  if (!href) return { sellerId: null, sellerName: null };

  const sellerMatch = href.match(/seller=([A-Z0-9]+)/);
  const sellerId = sellerMatch ? sellerMatch[1] : null;
  const sellerName = anchor.text().trim();

  return { sellerId, sellerName };
}

// Function to extract seller name from store link
function getSellerNameFromStoreLink($) {
  const bylineElement = $('#bylineInfo');
  if (!bylineElement.length) return null;
  
  const bylineText = bylineElement.text().trim();
  if (!bylineText) return null;
  
  // Extract seller name from text like "Visit the Rave Doctor Store"
  const visitMatch = bylineText.match(/Visit the (.+?) Store/i);
  if (visitMatch && visitMatch[1]) {
    return visitMatch[1].trim();
  }
  
  // Fallback: if it doesn't match the "Visit the X Store" pattern, 
  // try other common patterns or return the cleaned text
  const brandMatch = bylineText.match(/Brand:\s*(.+)/i);
  if (brandMatch && brandMatch[1]) {
    return brandMatch[1].trim();
  }
  
  return null;
}

async function scrapeDataFromAsins(
  asins, // string or array of strings
  countryCode = "US",
  clientId = 1
) {
  if (!Array.isArray(asins)) {
    asins = [asins];
  }

  const topLevelDomain = getAmazonTLD(countryCode);
  const products = [];
  
  // Create directory for saving HTML files
  // const htmlDir = path.join(__dirname, '../../debug/asin-html-saves');
  // if (!fs.existsSync(htmlDir)) {
  //   fs.mkdirSync(htmlDir, { recursive: true });
  // }
  
  for (const asin of asins) {
    const url = `https://www.amazon.${topLevelDomain}/dp/${asin}`;
    console.log(`Fetching ASIN: ${asin} - ${url}`);

    let htmlData;
    try {
      htmlData = await getHtmlByProxy(url, clientId);
      
      // Save HTML data to file
      // const timestamp = new Date().toISOString().replace(/[:.]/g, '-').replace('T', '_').slice(0, -5);
      // const filename = `asin_${asin}_${countryCode}_${timestamp}.html`;
      // const filepath = path.join(htmlDir, filename);
      
      // fs.writeFileSync(filepath, htmlData, 'utf8');
      // console.log(`✅ Saved ASIN HTML to: ${filepath}`);
      
    } catch (err) {
      console.warn(`Failed to fetch ASIN ${asin}:`, err.message);
      continue;
    }

    const $ = cheerio.load(htmlData);

    // Title
    const title = $("#productTitle").text().trim();

    // Image
    const image = $("#imgTagWrapperId img").attr("data-a-dynamic-image")
      ? (() => {
          try {
            const imgJson = JSON.parse(
              $("#imgTagWrapperId img").attr("data-a-dynamic-image")
            );
            return Object.keys(imgJson)[0];
          } catch {
            return "";
          }
        })()
      : $("#landingImage").attr("src") || "";

    // Average rating
    const ratingText =
      $("span[data-hook='rating-out-of-text']").text().trim() ||
      $("#acrPopover").attr("title") ||
      "";
    const avgRating = parseFloat(ratingText.split(" ")[0]) || 0;

    // Total reviews
    const totalReviews =
      parseInt(
        $("#acrCustomerReviewText").first().first()
          .text()
          .replace(/[^0-9]/g, "")
          .trim()
      ) || 0;

    const categoryAndRank = getCategoryAndRank($)[0]?.category;

    // Get seller information
    const sellerInfo = getSellerInfo($);
    
    // If sellerName is null or empty, try to get it from store link
    if (!sellerInfo.sellerName || sellerInfo.sellerName.trim() === '') {
      const storeSellerName = getSellerNameFromStoreLink($);
      if (storeSellerName) {
        sellerInfo.sellerName = storeSellerName;
      }
    }
    
    console.log(`ASIN: ${asin}, Category: ${categoryAndRank}, Seller: ${sellerInfo.sellerName} (${sellerInfo.sellerId})`);

    products.push({
      asin,
      title,
      image,
      productLink: url,
      avgRating,
      totalReviews,
      category: categoryAndRank,
      sellerId: sellerInfo.sellerId,
      sellerName: sellerInfo.sellerName,
    });
  }

  // console.log(`📁 ASIN HTML files saved to: ${htmlDir}`);
  return products;
}

// Example usage
async function Example() {
  try {
    const asins = ["B08141VNYF", "B0748P4TQQ"];
    const countryCode = "US";

    const products = await scrapeDataFromAsins(asins, countryCode, 1);
    console.log({ products });
  } catch (error) {
    console.error("Error during scraping:", error.message);
  }
}

// Example();

module.exports = { scrapeDataFromAsins };
