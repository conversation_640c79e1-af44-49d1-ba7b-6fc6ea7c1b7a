{
  "info": {
    "name": "LEX Prompts & Violation Detection API",
    "description": "Unified API collection for LEX prompt chain management, execution, and violation detection.",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "variable": [
    {
      "key": "baseUrl",
      "value": "http://localhost:3000",
      "type": "string"
    },
    {
      "key": "promptChainId",
      "value": "1",
      "type": "string"
    },
    {
      "key": "reviewId",
      "value": "1",
      "type": "string"
    },
    {
      "key": "jobId",
      "value": "1",
      "type": "string"
    }
  ],
  "item": [
    {
      "name": "Prompt Chain Management",
      "item": [
        {
          "name": "Get All Prompt Chains",
          "request": {
            "method": "GET",
            "header": [],
            "url": {
              "raw": "{{baseUrl}}/api/lex_prompts?active_only=false&limit=50",
              "host": ["{{baseUrl}}"],
              "path": ["api", "lex_prompts"],
              "query": [
                {
                  "key": "active_only",
                  "value": "false"
                },
                {
                  "key": "limit",
                  "value": "50"
                }
              ]
            }
          }
        },
        {
          "name": "Get Specific Prompt Chain",
          "request": {
            "method": "GET",
            "header": [],
            "url": {
              "raw": "{{baseUrl}}/api/lex_prompts/{{promptChainId}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "lex_prompts", "{{promptChainId}}"]
            }
          }
        },
        {
          "name": "Create New Prompt Chain",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"name\": \"Client-Specific Analysis Chain\",\n  \"description\": \"A prompt chain for client reviews.\",\n  \"prompt1\": \"Analyze review: {{reviewContent}}\",\n  \"prompt2\": \"Summarize review: {{reviewContent}}\",\n  \"prompt3\": \"Detect violations in: {{reviewContent}}\",\n  \"model\": \"azure-gpt4o\",\n  \"isActive\": true,\n  \"isPrimary\": false,\n  \"isClient\": true\n}"
            },
            "url": {
              "raw": "{{baseUrl}}/api/lex_prompts",
              "host": ["{{baseUrl}}"],
              "path": ["api", "lex_prompts"]
            }
          }
        },
        {
          "name": "Update Prompt Chain",
          "request": {
            "method": "PUT",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"name\": \"Updated Client-Specific Analysis Chain\",\n  \"isClient\": true\n}"
            },
            "url": {
              "raw": "{{baseUrl}}/api/lex_prompts/{{promptChainId}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "lex_prompts", "{{promptChainId}}"]
            }
          }
        },
        {
          "name": "Delete Prompt Chain",
          "request": {
            "method": "DELETE",
            "header": [],
            "url": {
              "raw": "{{baseUrl}}/api/lex_prompts/{{promptChainId}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "lex_prompts", "{{promptChainId}}"]
            }
          }
        }
      ]
    },
    {
      "name": "Prompt Chain Execution (All Prompts)",
      "item": [
        {
          "name": "Execute All Prompts for Single Review",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"reviewIds\": [{{reviewId}}],\n  \"promptChainId\": {{promptChainId}}\n}"
            },
            "url": {
              "raw": "{{baseUrl}}/api/lex_prompts/run-integrated",
              "host": ["{{baseUrl}}"],
              "path": ["api", "lex_prompts", "run-integrated"]
            },
            "description": "Execute all prompts (1, 2, and 3 for violation detection) on a single review."
          }
        },
        {
          "name": "Execute All Prompts for Multiple Reviews",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"reviewIds\": [1, 2, 3],\n  \"promptChainId\": {{promptChainId}}\n}"
            },
            "url": {
              "raw": "{{baseUrl}}/api/lex_prompts/run-integrated",
              "host": ["{{baseUrl}}"],
              "path": ["api", "lex_prompts", "run-integrated"]
            },
            "description": "Execute all prompts (1, 2, and 3 for violation detection) on multiple reviews."
          }
        }
      ]
    },
    {
      "name": "Violation Detection (Prompt 3 - Manual Trigger)",
      "item": [
        {
          "name": "Run Violation Detection - Single Review",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"reviewIds\": [{{reviewId}}],\n  \"model\": \"azure-gpt4o\"\n}"
            },
            "url": {
              "raw": "{{baseUrl}}/api/lex_prompts/run-violation",
              "host": ["{{baseUrl}}"],
              "path": ["api", "lex_prompts", "run-violation"]
            },
            "description": "Manually trigger violation detection (Prompt 3) for a single review. Requires the review to have been processed by a prompt chain previously."
          }
        },
        {\n          "name": "Run Violation Detection - Multiple Reviews (Gemini)",\n          "request": {\n            "method": "POST",\n            "header": [\n              {\n                "key": "Content-Type",\n                "value": "application/json"\n              }\n            ],\n            "body": {\n              "mode": "raw",\n              "raw": "{\n  \"reviewIds\": [1, 2, 3, 4, 5],\n  \"model\": \"gemini-1.5-flash\"\n}"\n            },\n            "url": {\n              "raw": "{{baseUrl}}/api/lex_prompts/run-violation",\n              "host": ["{{baseUrl}}"],\n              "path": ["api", "lex_prompts", "run-violation"]\n            },\n            "description": "Manually trigger violation detection (Prompt 3) for multiple reviews. Requires reviews to have been processed by a prompt chain previously."
          }\n        }\n      ]\n    },\n    {\n      "name": "Configuration & Utilities",\n      "item": [\n        {\n          "name": "Get Available AI Models",\n          "request": {\n            "method": "GET",\n            "header": [],\n            "url": {\n              "raw": "{{baseUrl}}/api/lex_prompts/models",\n              "host": ["{{baseUrl}}"],\n              "path": ["api", "lex_prompts", "models"]\n            }\n          }\n        },\n        {\n          "name": "Get Available Variables",\n          "request": {\n            "method": "GET",\n            "header": [],\n            "url": {\n              "raw": "{{baseUrl}}/api/lex_prompts/variables",\n              "host": ["{{baseUrl}}"],\n              "path": ["api", "lex_prompts", "variables"]\n            }\n          }\n        },\n        {\n          "name": "Get Job Status",\n          "request": {\n            "method": "GET",\n            "header": [],\n            "url": {\n              "raw": "{{baseUrl}}/api/lex_prompts/jobs/{{jobId}}",\n              "host": ["{{baseUrl}}"],\n              "path": ["api", "lex_prompts", "jobs", "{{jobId}}"]\n            }\n          }\n        }\n      ]\n    }\n  ]\n}