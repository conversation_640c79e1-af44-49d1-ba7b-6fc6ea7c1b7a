// Azure Function equivalent for image and PDF generation
const puppeteer = require("puppeteer");
const axios = require("axios");
const { uploadImage, uploadPDF } = require("../blobStorage");
require("dotenv").config();

async function generatePDF(url, companyName) {
  console.log("Generating PDF for company:", companyName);
  
  const browser = await puppeteer.launch({
    headless: true,
    args: ["--no-sandbox", "--disable-setuid-sandbox"],
    timeout: 60000, // 60 seconds
  });
  
  try {
    const page = await browser.newPage();
    await page.goto(url, { waitUntil: "networkidle2" });

    const pdfBuffer = await page.pdf({
      format: "A4",
      printBackground: true,
      preferCSSPageSize: true,
      margin: { top: "1cm", right: "1cm", bottom: "1cm", left: "1cm" },
      scale: 0.6,
      landscape: false,
    });

    await uploadPDF(pdfBuffer, `${companyName}.pdf`);
    console.log("PDF generated and uploaded successfully to Azure Blob Storage");
    
    return {
      success: true,
      message: "PDF generated and uploaded successfully",
      fileName: `${companyName}.pdf`
    };
  } catch (error) {
    console.error(
      `Error occurred while taking pdf of company- ${companyName} : `,
      error.message
    );
    throw error;
  } finally {
    await browser.close();
  }
}

async function generateImages(url, slug) {
  try {
    console.log("Capturing product for:", slug);
    
    const browser = await puppeteer.launch({
      headless: true,
      args: ["--no-sandbox", "--disable-setuid-sandbox"],
      timeout: 60000,
    });

    const page = await browser.newPage();

    // Use ScraperAPI to handle the request
    const scraperApiKey = process.env.SCRAPER_API_KEY || "********************************";
    const scraperApiUrl = `http://api.scraperapi.com?api_key=${scraperApiKey}&url=${encodeURIComponent(url)}`;
    
    const response = await axios.get(scraperApiUrl);
    const htmlContent = response.data;

    await page.setContent(htmlContent, { waitUntil: "networkidle2" });
    
    // Handle cookie acceptance
    const acceptBtnSelector = "#sp-cc-accept";
    const element = await page.$(acceptBtnSelector);
    if (element) {
      console.log("Cookie Accept Button Pressed.");
      await page.click(acceptBtnSelector);
    } else {
      console.log("No Cookie Accept Button.");
    }
    
    await page.setViewport({
      width: 1920,
      height: 1080,
      deviceScaleFactor: 2,
    });

    // Capture product image
    const productImage = await page.$(".imageBlockRearch");
    if (productImage) {
      const box = await productImage.boundingBox();

      const productImageBuffer = await page.screenshot({
        encoding: "binary",
        clip: {
          x: box.x,
          y: box.y,
          width: box.width + 20,
          height: box.height + 20,
        },
      });
      
      await uploadImage(productImageBuffer, `${slug}_product_image.png`);
      console.log("Product image captured and uploaded successfully");
    } else {
      console.log("Product image not found");
    }

    // Capture whole page
    const wholePageBuffer = await page.screenshot({
      encoding: "binary",
    });

    await uploadImage(wholePageBuffer, `${slug}_page_image.png`);

    await browser.close();

    console.log("Screenshots captured and uploaded successfully to Azure Blob Storage");
    
    return {
      success: true,
      message: "Images generated and uploaded successfully",
      files: [`${slug}_product_image.png`, `${slug}_page_image.png`]
    };
  } catch (error) {
    console.error("Error occurred while capturing product:", error.message);
    throw error;
  }
}

// Azure Function handler equivalent
async function processImageRequest(context) {
  const { action, url, identifier } = context.req.body || context;

  try {
    let result;
    
    if (action === "generatePDF") {
      result = await generatePDF(url, identifier);
    } else if (action === "generateImages") {
      result = await generateImages(url, identifier);
    } else {
      throw new Error("Invalid action specified. Use 'generatePDF' or 'generateImages'");
    }

    return {
      status: 200,
      body: {
        success: true,
        message: "Operation completed successfully",
        result: result
      }
    };
  } catch (error) {
    console.error("Error in Azure Function:", error);
    
    return {
      status: 500,
      body: {
        success: false,
        message: error.message,
        error: error.toString()
      }
    };
  }
}

// Local execution function for testing
async function executeLocally(action, url, identifier) {
  console.log(`Executing ${action} locally for ${identifier}`);
  
  const context = {
    req: {
      body: { action, url, identifier }
    }
  };
  
  return await processImageRequest(context);
}

module.exports = {
  generatePDF,
  generateImages,
  processImageRequest,
  executeLocally
};

// Example usage for testing (commented out)
// executeLocally(
//   "generateImages",
//   "https://www.amazon.co.uk/Enhanced-Pomegranate-Ingredients-Free-Soul/dp/B08WPFV3VC/ref=sr_1_1?dib=eyJ2IjoiMSJ9.W-lL_mvp_m2sBQRPvshc_ntqVqUYQW3XQaqFvjDylyoLJG2Y535zJ5IzqdNaNhDWKT14ZyzqCoksXrRk0f00lAAejwFTUWbthPdn5UO2JDPBlUOvgs1HnhWvsyiduiFibAh7X4l_NQZTGbPWzHsr5yxXMBkPw43_E5pG92M2oJB3UbMFRiwc9pkTCA-ct11Wd3MH6L921o-nDhzsWOOQMpON-EHsL46h3LRQON2Yr6M.lE8rrb7j8l5xUnJxXxRNkMsSfMr2IIwDX_s5YRauvm0&dib_tag=se&m=A1SFIMCH4IZ3GA&qid=1727092532&s=merchant-items&sr=1-1",
//   "ZLINE"
// ).then(result => {
//   console.log("Result:", result);
// }).catch(console.error);
