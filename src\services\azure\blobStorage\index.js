const { BlobServiceClient } = require("@azure/storage-blob");
require("dotenv").config();

// Azure Blob Storage configuration
const accountName = process.env.AZURE_STORAGE_ACCOUNT_NAME;
const accountKey = process.env.AZURE_STORAGE_ACCOUNT_KEY;
const containerName = process.env.AZURE_STORAGE_CONTAINER_NAME || "eq-assets";

const imageFolder = "images/";
const pdfFolder = "pdfs/";
const jeffResultsFolder = "jeff-results/";

if (!accountName || !accountKey) {
  console.warn('Azure Storage credentials are not properly configured in environment variables.');
  console.warn('Please ensure AZURE_STORAGE_ACCOUNT_NAME and AZURE_STORAGE_ACCOUNT_KEY are set in the .env file.');
}

// Create BlobServiceClient
const blobServiceClient = new BlobServiceClient(
  `https://${accountName}.blob.core.windows.net`,
  {
    accountName,
    accountKey
  }
);

// Get container client
const containerClient = blobServiceClient.getContainerClient(containerName);

function getBlobUrl(blobName, folder = imageFolder) {
  return `https://${accountName}.blob.core.windows.net/${containerName}/${folder}${blobName}`;
}

async function uploadImage(imageBuffer, imageName, folder = imageFolder, container = containerName) {
  const blobName = `${folder}${imageName}`;
  
  try {
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);
    
    const uploadOptions = {
      blobHTTPHeaders: {
        blobContentType: "image/jpeg"
      }
    };

    const uploadResponse = await blockBlobClient.upload(imageBuffer, imageBuffer.length, uploadOptions);
    console.log(imageName + " uploaded successfully to Azure Blob Storage");
    
    return {
      ...uploadResponse,
      url: getBlobUrl(imageName, folder)
    };
  } catch (error) {
    console.error("Error uploading image to Azure Blob Storage:", error);
    throw error;
  }
}

async function deleteImage(imageName, folder = imageFolder) {
  const blobName = `${folder}${imageName}`;
  
  try {
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);
    const deleteResponse = await blockBlobClient.delete();
    console.log(imageName + " deleted successfully from Azure Blob Storage");
    return deleteResponse;
  } catch (error) {
    console.error("Error deleting image from Azure Blob Storage:", error);
    throw error;
  }
}

async function uploadPDF(pdfBuffer, pdfName, folder = pdfFolder) {
  const blobName = `${folder}${pdfName}`;
  
  try {
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);
    
    const uploadOptions = {
      blobHTTPHeaders: {
        blobContentType: "application/pdf"
      }
    };

    const uploadResponse = await blockBlobClient.upload(pdfBuffer, pdfBuffer.length, uploadOptions);
    console.log("PDF uploaded successfully to Azure Blob Storage");
    
    return {
      ...uploadResponse,
      url: getBlobUrl(pdfName, folder)
    };
  } catch (error) {
    console.error("Error uploading PDF to Azure Blob Storage:", error);
    throw error;
  }
}

async function isValidBlobLink(blobUrl) {
  try {
    const url = new URL(blobUrl);
    const pathParts = url.pathname.split('/');
    
    if (pathParts.length < 3) {
      return false;
    }
    
    const containerName = pathParts[1];
    const blobName = pathParts.slice(2).join('/');
    
    const containerClient = blobServiceClient.getContainerClient(containerName);
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);
    
    const exists = await blockBlobClient.exists();
    return exists;
  } catch (error) {
    console.error("Error checking blob link validity:", error);
    return false;
  }
}

async function uploadCsv(csvBuffer, csvName, folder = jeffResultsFolder) {
  const blobName = `${folder}${csvName}`;
  
  try {
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);
    
    const uploadOptions = {
      blobHTTPHeaders: {
        blobContentType: "text/csv"
      }
    };

    const uploadResponse = await blockBlobClient.upload(csvBuffer, csvBuffer.length, uploadOptions);
    console.log("CSV uploaded successfully to Azure Blob Storage");
    
    return {
      ...uploadResponse,
      url: getBlobUrl(csvName, folder)
    };
  } catch (error) {
    console.error("Error uploading CSV to Azure Blob Storage:", error);
    throw error;
  }
}

async function exampleUsage() {
  try {
    // Example usage would go here
    console.log("Azure Blob Storage service initialized successfully");
    console.log(`Container: ${containerName}`);
    console.log(`Account: ${accountName}`);
  } catch (error) {
    console.error("Error in example usage:", error);
  }
}

// Call exampleUsage to test the functions
if (require.main === module) {
  exampleUsage();
}

module.exports = {
  uploadImage,
  uploadPDF,
  isValidBlobLink,
  getBlobUrl,
  deleteImage,
  uploadCsv
};
