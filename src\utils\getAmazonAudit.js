const cheerio = require("cheerio");
const fs = require("fs");
const prisma = require("../database/prisma/getPrismaClient");
const { getHtmlByProxy } = require("./getHtmlByProxy");
const { TARGET_URL } = require("../services/scrapeAmazon/constant");
const getProductDetails = require("./scrapeAmazonUtils/getProductDetails");
const isSponsoredCheck = require("./scrapeAmazonUtils/getSponsoredCheck");
const storeFront = require("./scrapeAmazonUtils/getStoreFront");
const getAuditedJson = require("../services/amazonAudit");
const generateImages = require("../utils/scrapeAmazonUtils/screenshotHelper/getAmazonScreenshots.js");
const generatePDF = require("../utils/scrapeAmazonUtils/generatePDF");
const getProductAsins = require("../utils/scrapeAmazonUtils/getProductAsins");
const triggerLambda = require("../services/aws/lambda/trigger");
const { generateSlug } = require("./generateSlug");
const { isValidS3Link } = require("../services/aws/s3/index");
const getAmazonScreenshots = require("./scrapeAmazonUtils/screenshotHelper/getAmazonScreenshots.js");
const auditImageScreenshot = require("./scrapeAmazonUtils/screenshotHelper/generateMailAuditScreenShot");
const createAndUpdateUTMUrl = require("./utmHelpers/createAndUpdateUTMUrl.js");
const { getClientName } = require("./utils.js");
require("dotenv").config();


async function getAmazonAudit({
  sellerName,
  productUrl,
  client,
  emailId,
  clientId,
  regenerate,
}) {
  try {
    console.log("Getting Amazon Audit ---------");
    let productDetails = {};
    let isProductURLProvided = false;
    let finalProductURL = "";
    let searchPageHtml = "";
    let companyName = sellerName;
    let sellerBusinessName = sellerName;

    if (!productUrl && client == "hunter") {
      const result = await prisma.hunterSellerTable.findFirst({
        where: {
          OR: [
            { sellerName: companyName },
            { sellerBusinessName: companyName },
            { sellerEmail: emailId },
          ],
        },
        select: {
          sellerName: true,
          bestSellingUrl: true,
          sellerBusinessName: true,
        },
      });
      console.log("RESULT FROM PRISMA:", result);
      if (result) {
        finalProductURL = result.bestSellingUrl;
        companyName = companyName ? companyName : result.sellerName;
        sellerBusinessName = result.sellerBusinessName
          ? result.sellerBusinessName
          : companyName;
      }
    }

    if (productUrl) {
      console.log("PRODUCT URL PRESENT...");
      isProductURLProvided = true;
      finalProductURL = productUrl;
    } else {
      console.log("NO PRODUCT URL FOUND, SEARCHING ON AMAZON...");
      const formatSearchKeyword = companyName
        .trim()
        .replace(/[^\w\s-]/g, "")
        .replace(/\s+/g, "+")
        .replace(/^-+|-+$/g, "");
      const searchUrl = `${TARGET_URL}/s?k=${formatSearchKeyword}`;
      console.log("SEARCH URL:", searchUrl);
      searchPageHtml = await getHtmlByProxy(searchUrl);

      const productAsins = getProductAsins({
        htmlData: searchPageHtml,
        keyword: companyName,
        numOfProducts: 1,
      });
      const asin = productAsins[0];
      console.log("ASIN:", asin);
      finalProductURL = `${TARGET_URL}/dp/${asin}`;
    }
    console.log({ finalProductURL });
    if (finalProductURL) {
      console.log("FINAL PRODUCT URL:", finalProductURL);
      const productHTML = await getHtmlByProxy(finalProductURL, clientId);
      productDetails = getProductDetails(productHTML, companyName);
      console.log("PRODUCT DETAILS:", productDetails);
      const $ = cheerio.load(productHTML);

      const transformedProductDetails = {
        company_name: companyName,
        store: storeFront($),
        productData: [productDetails],
      };

      const amazonAuditReport = await getAuditedJson(
        transformedProductDetails,
        clientId,
        process.env.ENABLE_YML_AUDIT
      );

      const slug = generateSlug(String(sellerBusinessName || sellerName));

      const report = await prisma.amazonAuditReport.create({
        data: {
          companyName: companyName,
          productUrl: finalProductURL || productDetails.url,
          amazonAudit: productDetails,
          sellerEmail: emailId,
          amazonAuditReport: amazonAuditReport,
          isProductURLProvided: isProductURLProvided,
          slug: slug,
          finalUrl: "",
          pageImage: "",
          productImage: "",
          pdfUrl: "",
          category: "",
        },
      });
      newAuditReport.category =
        productDetails.categoryAndRank &&
        productDetails.categoryAndRank.length > 0
          ? productDetails.categoryAndRank[0].category
          : "";
      // console.log("AUDIT REPORT:", newAuditReport);
      if (regenerate == "true") {
        const finalUrl =
          "https://www.equalcollective.com/gigabrains/amazonaudit/" +
          newAuditReport.slug;
        newAuditReport.finalUrl = finalUrl;
        newAuditReport.pageImage =
          "https://eq--assets.s3.ap-south-1.amazonaws.com/images/" +
          newAuditReport.slug +
          "_page_image.png";
        newAuditReport.productImage =
          "https://eq--assets.s3.ap-south-1.amazonaws.com/images/" +
          newAuditReport.slug +
          "_product_image.png";
        newAuditReport.pdfUrl =
          "https://eq--assets.s3.ap-south-1.amazonaws.com/pdfs/" +
          newAuditReport.slug +
          ".pdf";
        console.log("FINAL URL:", finalUrl);
        console.log("PAGE IMAGE:", newAuditReport.pageImage);
        console.log("PRODUCT IMAGE:", newAuditReport.productImage);
        console.log("PDF URL:", newAuditReport.pdfUrl);
        console.log("CATEGORY:", newAuditReport.category);

        await prisma.amazonAuditReport.update({
          where: { id: newAuditReport.id },
          data: {
            finalUrl: finalUrl,
            pageImage: newAuditReport.pageImage,
            productImage: newAuditReport.productImage,
            pdfUrl: newAuditReport.pdfUrl,
            category: newAuditReport.category,
          },
        });
        // await generateImages(newAuditReport.productUrl, newAuditReport.slug);
        await generatePDF(finalUrl, newAuditReport.slug);
        await auditImageScreenshot(finalUrl, newAuditReport.slug);
      }
      // console.log("FINAL REPORT:", newAuditReport);
      return newAuditReport;
    }
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error(
      "Error Getting Amazon Audit For:",
      sellerName,
      "---Error:",
      error
    );
  }
}

async function generateAuditScreenshots(csvData) {
  const slug = csvData.productSlug;
  const companyId = csvData.companyId;
  const report = await prisma.amazonAuditReport.findFirst({
    where: {
      slug: slug,
    },
    orderBy: {
      updatedAt: "desc",
    },
  });
  
  // Helper function to extract category
  const getCategory = (amazonAudit) => {
    return amazonAudit.categoryAndRank && amazonAudit.categoryAndRank.length > 0
      ? amazonAudit.categoryAndRank[0].category
      : "";
  };

  // Helper function to generate URLs
  const generateUrls = async (slug, clientName, companyName) => {
    const baseUrl = "https://eq--assets.s3.ap-south-1.amazonaws.com";
    const redirectAuditWebLink = await createAndUpdateUTMUrl({
      url: `https://www.equalcollective.com/jeff/audit/${slug}`,
      clientName: clientName,
      sellerId: companyName,
      type: "audit",
      email: csvData.email || "",
      campaign: csvData.campaignName,
    });
    return {
      finalUrl: redirectAuditWebLink,
      pageImage: `${baseUrl}/images/${slug}_page_image.png`,
      productImage: `${baseUrl}/images/${slug}_product_image.png`,
    };
  };

  try {
    if (report) {
      report.category = getCategory(report.amazonAudit);
      const clientId = parseInt(report.slug.split("-").pop(), 10);
      const clientName = await getClientName(clientId);
      const urls = await generateUrls(
        report.slug,
        clientName,
        report.companyName
      );
      report.category = getCategory(report.amazonAudit);

      console.log("FINAL URL:", urls.finalUrl);
      console.log("PAGE IMAGE:", urls.pageImage);
      console.log("PRODUCT IMAGE:", urls.productImage);
      console.log("CATEGORY:", report.category);
      
      if (!report.pageImage || !report.productImage) {
        await getAmazonScreenshots(clientId, report.productUrl, slug, companyId);
        await prisma.amazonAuditReport.update({
          where: { id: report.id },
          data: {
            finalUrl: urls.finalUrl,
            pageImage: (await isValidS3Link(urls.pageImage))
              ? urls.pageImage
              : "",
            productImage: (await isValidS3Link(urls.productImage))
              ? urls.productImage
              : "",
            category: report.category,
          },
        });
      } else {
        console.log("Page and Product Screenshots Already Present");
      }
      
      const redirectProductUrl = await createAndUpdateUTMUrl({
        url:
          csvData.prospectDetails.productAmazonURL ||
          csvData.prospectDetails.bestSellingProductURL,
        clientName: clientName,
        sellerId: csvData.companyName,
        type: "amazon",
        campaign: csvData.campaignName,
        email: csvData.email || "",
      });
      await prisma.amazonAuditReport.update({
        where: { id: report.id },
        data: {
          productUrl: redirectProductUrl,
        },
      });
    }
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error generating audit screenshots:", error);
  }
}

async function generateAuditPdf(csvData) {
  const slug = csvData.productSlug;
  const companyId = csvData.companyId;
  const report = await prisma.amazonAuditReport.findFirst({
    where: {
      slug: slug,
    },
    orderBy: {
      updatedAt: "desc",
    },
  });
  // Helper function to extract category
  const getCategory = (amazonAudit) => {
    return amazonAudit.categoryAndRank && amazonAudit.categoryAndRank.length > 0
      ? amazonAudit.categoryAndRank[0].category
      : "";
  };

  // Helper function to generate URLs
  const generateUrls = async (slug, clientName, companyName) => {
    const baseUrl = "https://eq--assets.s3.ap-south-1.amazonaws.com";
    const redirectAuditWebLink = await createAndUpdateUTMUrl({
      url: `https://www.equalcollective.com/jeff/audit/${slug}`,
      clientName: clientName,
      sellerId: companyName,
      type: "audit",
      email: csvData.email || "",
      campaign: csvData.campaignName,
    });
    const redirectAuditPdfLink = await createAndUpdateUTMUrl({
      url: `${baseUrl}/pdfs/${slug}.pdf`,
      clientName: clientName,
      sellerId: companyName,
      type: "pdf",
      email: csvData.email || "",
      campaign: csvData.campaignName,
    });
    return {
      finalUrl: redirectAuditWebLink,
      pageImage: `${baseUrl}/images/${slug}_page_image.png`,
      productImage: `${baseUrl}/images/${slug}_product_image.png`,
      pdfUrl: redirectAuditPdfLink,
    };
  };

  try {
    // console.log("REPORT:", report);

    if (report) {
      report.category = getCategory(report.amazonAudit);
      // console.log("AUDIT REPORT:", report);
      const clientId = parseInt(report.slug.split("-").pop(), 10);
      const clientName = await getClientName(clientId);
      const urls = await generateUrls(
        report.slug,
        clientName,
        report.companyName
      );
      // report.finalUrl = urls.finalUrl;
      // report.pageImage = urls.pageImage;
      // report.productImage = urls.productImage;
      // report.pdfUrl = urls.pdfUrl;
      report.category = getCategory(report.amazonAudit);

      console.log("FINAL URL:", urls.finalUrl);
      console.log("PAGE IMAGE:", urls.pageImage);
      console.log("PRODUCT IMAGE:", urls.productImage);
      console.log("PDF URL:", urls.pdfUrl);
      console.log("CATEGORY:", report.category);
      if (!report.pageImage || !report.productImage) {
        await getAmazonScreenshots(clientId,report.productUrl, slug, companyId);
        await prisma.amazonAuditReport.update({
          where: { id: report.id },
          data: {
            finalUrl: urls.finalUrl,
            pageImage: (await isValidS3Link(urls.pageImage))
              ? urls.pageImage
              : "",
            productImage: (await isValidS3Link(urls.productImage))
              ? urls.productImage
              : "",
            category: report.category,
          },
        });
      } else {
        console.log("Page and Product Screenshots Already Present");
      }
      const redirectProductUrl = await createAndUpdateUTMUrl({
        url:
          csvData.prospectDetails.productAmazonURL ||
          csvData.prospectDetails.bestSellingProductURL,
        clientName: clientName,
        sellerId: csvData.companyName,
        type: "amazon",
        campaign: csvData.campaignName,
        email: csvData.email || "",
      });
      await prisma.amazonAuditReport.update({
        where: { id: report.id },
        data: {
          productUrl: redirectProductUrl,
        },
      });

      // await triggerLambda("generatePDF", urls.finalUrl, report.slug);
      // await generateImages(report.productUrl, slug, clientId);
      const urlForPdf = `https://www.equalcollective.com/jeff/audit/${slug}?source=pdf`;
      await auditImageScreenshot(urlForPdf, slug);
      const pdfCreated = await generatePDF(urlForPdf, slug);
      if (pdfCreated) {
        await prisma.amazonAuditReport.update({
          where: { id: report.id },
          data: {
            pdfUrl: urls.pdfUrl,
          },
        });
      }

      // console.log("FINAL REPORT:", report);
    }
  } catch (error) {
    console.error("Error Stack:", error.stack);
    console.error("Error generating audit PDF:", error);
  }
}

module.exports = { getAmazonAudit, generateAuditPdf, generateAuditScreenshots };
