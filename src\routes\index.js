const express = require("express");
const jwt = require("jsonwebtoken");

const router = express.Router();

const adhocRoutes = require("./adhoc/getAmazonProductDetails");
router.use(adhocRoutes);

const calendlyRoute = require("./calendly");
router.use(calendlyRoute);

// Import user routes
const userRoutes = require("./user");
router.use(userRoutes);

// Import pipeline routes
const pipelineRoutes = require("./pipeline");
router.use(pipelineRoutes);

// Import requestLog routes
// const requestLogRoutes = require("./requestLog");
// router.use(requestLogRoutes);

// Import auditReport routes
const auditReportRoutes = require("./auditReport");
router.use(auditReportRoutes);

// Import job routes
const jobRoutes = require("./job");
router.use(jobRoutes);

// Import replyAutomation routes
const replyAutomation = require("./replyAutomation");
router.use(replyAutomation);

// Import configuration routes
const configurationRoutes = require("./configuration");
router.use(configurationRoutes);

const googleSheetCreationRoutes = require("./googleSheet");
router.use(googleSheetCreationRoutes);

// Import LEX routes
const lexRoutes = require("./lex");
router.use(lexRoutes);

// Import LEX Reviews routes
const lexReviewsRoutes = require("./lexReviews");
router.use(lexReviewsRoutes);

// Import LEX Checker routes
const lexCheckerRoutes = require("./lexChecker");
router.use(lexCheckerRoutes);

// Import LEX Prompts routes
const lexPromptsRoutes = require("./lex_prompts");
router.use(lexPromptsRoutes);

// Import AWS instance management routes
const awsInstanceRoutes = require("./awsInstance");
router.use(awsInstanceRoutes);

// Import Lex Image Generation routes
const lexImageGenRoutes = require("./lexImageGen");
router.use(lexImageGenRoutes);

const spikeRoutes = require("./spike");
router.use(spikeRoutes);

module.exports = router;
